com.a2ia.data.Callback"com.a2ia.data.CheckDocumentResults,com.a2ia.data.CheckDocumentResults.Companion)com.a2ia.data.CheckDocumentResults.Result(com.a2ia.data.CheckDocumentResults.Field)com.a2ia.data.CheckDocumentResults.Amount*com.a2ia.data.CheckDocumentResults.CARItem-com.a2ia.data.CheckDocumentResults.DateResult2com.a2ia.data.CheckDocumentResults.AddressLineItem-com.a2ia.data.CheckDocumentResults.Invaliditycom.a2ia.data.CheckInput"com.a2ia.data.CheckInput.Companioncom.a2ia.data.ProcessorLogs com.a2ia.data.ChequeProcessorSDK*com.a2ia.data.ChequeProcessorSDK.Companioncom.a2ia.data.ChequeUtils!com.a2ia.data.ClockwiseComparatorcom.a2ia.data.ImageFormatcom.a2ia.data.OutputFormatcom.a2ia.data.ImageConvertercom.a2ia.data.A2iABooleancom.a2ia.data.ImageOutputFormatcom.a2ia.data.VerboseDetailscom.a2ia.data.MemoryImagecom.a2ia.data.ProcessedImage&com.a2ia.data.ProcessedImage.Companioncom.a2ia.data.Inputcom.a2ia.data.OnnxModelHandler(com.a2ia.data.OnnxModelHandler.Companioncom.a2ia.data.TFLiteHandler%com.a2ia.data.TFLiteHandler.Companioncom.a2ia.data.YOLOModelHandler(com.a2ia.data.YOLOModelHandler.Companion com.a2ia.data.ml.ModelV2.Outputscom.a2ia.data.ml.ModelV2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           