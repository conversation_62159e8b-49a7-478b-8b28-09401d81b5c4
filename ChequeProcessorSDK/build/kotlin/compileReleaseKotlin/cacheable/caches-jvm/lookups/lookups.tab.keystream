  
OnnxTensor ai.onnxruntime  	OnnxValue ai.onnxruntime  OrtEnvironment ai.onnxruntime  OrtException ai.onnxruntime  
OrtSession ai.onnxruntime  createTensor ai.onnxruntime.OnnxTensor  getUSE ai.onnxruntime.OnnxTensor  getUse ai.onnxruntime.OnnxTensor  use ai.onnxruntime.OnnxTensor  use ai.onnxruntime.OnnxTensorLike  getVALUE ai.onnxruntime.OnnxValue  getValue ai.onnxruntime.OnnxValue  setValue ai.onnxruntime.OnnxValue  value ai.onnxruntime.OnnxValue  close ai.onnxruntime.OrtEnvironment  
createSession ai.onnxruntime.OrtEnvironment  getEnvironment ai.onnxruntime.OrtEnvironment  getLET ai.onnxruntime.OrtEnvironment  getLet ai.onnxruntime.OrtEnvironment  let ai.onnxruntime.OrtEnvironment  message ai.onnxruntime.OrtException  Result ai.onnxruntime.OrtSession  close ai.onnxruntime.OrtSession  run ai.onnxruntime.OrtSession  get  ai.onnxruntime.OrtSession.Result  getUSE  ai.onnxruntime.OrtSession.Result  getUse  ai.onnxruntime.OrtSession.Result  use  ai.onnxruntime.OrtSession.Result  Context android.content  applicationContext android.content.Context  assets android.content.Context  getAPPLICATIONContext android.content.Context  	getASSETS android.content.Context  getApplicationContext android.content.Context  	getAssets android.content.Context  getExternalFilesDir android.content.Context  setApplicationContext android.content.Context  	setAssets android.content.Context  AssetFileDescriptor android.content.res  declaredLength 'android.content.res.AssetFileDescriptor  fileDescriptor 'android.content.res.AssetFileDescriptor  getDECLAREDLength 'android.content.res.AssetFileDescriptor  getDeclaredLength 'android.content.res.AssetFileDescriptor  getFILEDescriptor 'android.content.res.AssetFileDescriptor  getFileDescriptor 'android.content.res.AssetFileDescriptor  getSTARTOffset 'android.content.res.AssetFileDescriptor  getStartOffset 'android.content.res.AssetFileDescriptor  setDeclaredLength 'android.content.res.AssetFileDescriptor  setFileDescriptor 'android.content.res.AssetFileDescriptor  setStartOffset 'android.content.res.AssetFileDescriptor  startOffset 'android.content.res.AssetFileDescriptor  open  android.content.res.AssetManager  openFd  android.content.res.AssetManager  Bitmap android.graphics  
BitmapFactory android.graphics  BlurMaskFilter android.graphics  Canvas android.graphics  Color android.graphics  ColorFilter android.graphics  ColorMatrix android.graphics  ColorMatrixColorFilter android.graphics  
MaskFilter android.graphics  Matrix android.graphics  Paint android.graphics  Rect android.graphics  CompressFormat android.graphics.Bitmap  Config android.graphics.Bitmap  also android.graphics.Bitmap  compress android.graphics.Bitmap  config android.graphics.Bitmap  copy android.graphics.Bitmap  createBitmap android.graphics.Bitmap  createScaledBitmap android.graphics.Bitmap  density android.graphics.Bitmap  equals android.graphics.Bitmap  getALSO android.graphics.Bitmap  getAlso android.graphics.Bitmap  	getCONFIG android.graphics.Bitmap  	getConfig android.graphics.Bitmap  
getDENSITY android.graphics.Bitmap  
getDensity android.graphics.Bitmap  	getHEIGHT android.graphics.Bitmap  	getHeight android.graphics.Bitmap  
getISRecycled android.graphics.Bitmap  
getIsRecycled android.graphics.Bitmap  	getPixels android.graphics.Bitmap  getWIDTH android.graphics.Bitmap  getWidth android.graphics.Bitmap  height android.graphics.Bitmap  
isRecycled android.graphics.Bitmap  recycle android.graphics.Bitmap  	setConfig android.graphics.Bitmap  
setDensity android.graphics.Bitmap  	setHeight android.graphics.Bitmap  setRecycled android.graphics.Bitmap  setWidth android.graphics.Bitmap  width android.graphics.Bitmap  JPEG &android.graphics.Bitmap.CompressFormat  PNG &android.graphics.Bitmap.CompressFormat  	ARGB_8888 android.graphics.Bitmap.Config  Options android.graphics.BitmapFactory  decodeByteArray android.graphics.BitmapFactory  
decodeFile android.graphics.BitmapFactory  decodeStream android.graphics.BitmapFactory  inPreferredConfig &android.graphics.BitmapFactory.Options  Blur android.graphics.BlurMaskFilter  NORMAL $android.graphics.BlurMaskFilter.Blur  
drawBitmap android.graphics.Canvas  	drawColor android.graphics.Canvas  drawRect android.graphics.Canvas  drawText android.graphics.Canvas  BLACK android.graphics.Color  GREEN android.graphics.Color  RED android.graphics.Color  WHITE android.graphics.Color  
postConcat android.graphics.ColorMatrix  apply android.graphics.Matrix  getAPPLY android.graphics.Matrix  getApply android.graphics.Matrix  getLET android.graphics.Matrix  getLet android.graphics.Matrix  let android.graphics.Matrix  
postRotate android.graphics.Matrix  	postScale android.graphics.Matrix  Color android.graphics.Paint  FILTER_BITMAP_FLAG android.graphics.Paint  Paint android.graphics.Paint  Style android.graphics.Paint  alpha android.graphics.Paint  android android.graphics.Paint  apply android.graphics.Paint  color android.graphics.Paint  colorFilter android.graphics.Paint  flags android.graphics.Paint  floatArrayOf android.graphics.Paint  getALPHA android.graphics.Paint  
getANDROID android.graphics.Paint  getAPPLY android.graphics.Paint  getAlpha android.graphics.Paint  
getAndroid android.graphics.Paint  getApply android.graphics.Paint  getCOLOR android.graphics.Paint  getCOLORFilter android.graphics.Paint  getColor android.graphics.Paint  getColorFilter android.graphics.Paint  getFLAGS android.graphics.Paint  getFLOATArrayOf android.graphics.Paint  getFlags android.graphics.Paint  getFloatArrayOf android.graphics.Paint  getISAntiAlias android.graphics.Paint  getISDither android.graphics.Paint  getISFilterBitmap android.graphics.Paint  getIsAntiAlias android.graphics.Paint  getIsDither android.graphics.Paint  getIsFilterBitmap android.graphics.Paint  
getMASKFilter android.graphics.Paint  
getMaskFilter android.graphics.Paint  getSTROKEWidth android.graphics.Paint  getSTYLE android.graphics.Paint  getStrokeWidth android.graphics.Paint  getStyle android.graphics.Paint  getTEXTSize android.graphics.Paint  
getTextBounds android.graphics.Paint  getTextSize android.graphics.Paint  isAntiAlias android.graphics.Paint  isDither android.graphics.Paint  isFilterBitmap android.graphics.Paint  
maskFilter android.graphics.Paint  measureText android.graphics.Paint  setAlpha android.graphics.Paint  setAntiAlias android.graphics.Paint  setColor android.graphics.Paint  setColorFilter android.graphics.Paint  	setDither android.graphics.Paint  setFilterBitmap android.graphics.Paint  setFlags android.graphics.Paint  
setMaskFilter android.graphics.Paint  setStrokeWidth android.graphics.Paint  setStyle android.graphics.Paint  setTextSize android.graphics.Paint  strokeWidth android.graphics.Paint  style android.graphics.Paint  textSize android.graphics.Paint  FILL android.graphics.Paint.Style  STROKE android.graphics.Paint.Style  centerY android.graphics.Rect  equals android.graphics.Rect  getLET android.graphics.Rect  getLet android.graphics.Rect  height android.graphics.Rect  left android.graphics.Rect  let android.graphics.Rect  top android.graphics.Rect  width android.graphics.Rect  Build 
android.os  Environment 
android.os  Log android.util  d android.util.Log  e android.util.Log  A2iABoolean 
com.a2ia.data  AddressLineItem 
com.a2ia.data  Any 
com.a2ia.data  Array 
com.a2ia.data  	ArrayList 
com.a2ia.data  AtomicReference 
com.a2ia.data  Bitmap 
com.a2ia.data  
BitmapFactory 
com.a2ia.data  Boolean 
com.a2ia.data  	ByteArray 
com.a2ia.data  ByteArrayOutputStream 
com.a2ia.data  
ByteBuffer 
com.a2ia.data  	ByteOrder 
com.a2ia.data  Callback 
com.a2ia.data  Canvas 
com.a2ia.data  CheckDocumentResults 
com.a2ia.data  
CheckInput 
com.a2ia.data  ChequeProcessorSDK 
com.a2ia.data  ChequeUtils 
com.a2ia.data  ClockwiseComparator 
com.a2ia.data  Collections 
com.a2ia.data  Color 
com.a2ia.data  
Comparator 
com.a2ia.data  Core 
com.a2ia.data  CountDownLatch 
com.a2ia.data  CvType 
com.a2ia.data  DEFAULT_MODEL_PATH 
com.a2ia.data  DataType 
com.a2ia.data  Date 
com.a2ia.data  Double 
com.a2ia.data  DoubleArray 
com.a2ia.data  	Exception 
com.a2ia.data  	Executors 
com.a2ia.data  File 
com.a2ia.data  FileChannel 
com.a2ia.data  FileInputStream 
com.a2ia.data  FileOutputStream 
com.a2ia.data  Float 
com.a2ia.data  
FloatArray 
com.a2ia.data  FloatBuffer 
com.a2ia.data  HashMap 
com.a2ia.data  
INPUT_SIZE 
com.a2ia.data  IllegalStateException 
com.a2ia.data  ImageConverter 
com.a2ia.data  ImageFormat 
com.a2ia.data  ImageOutputFormat 
com.a2ia.data  Imgproc 
com.a2ia.data  IncompatibleClassChangeError 
com.a2ia.data  Input 
com.a2ia.data  
InputImage 
com.a2ia.data  Int 
com.a2ia.data  IntArray 
com.a2ia.data  Interpreter 
com.a2ia.data  JvmField 
com.a2ia.data  	JvmStatic 
com.a2ia.data  KEY_CVN_CODE 
com.a2ia.data  KEY_CVN_RECT 
com.a2ia.data  
KEY_CVN_SCORE 
com.a2ia.data  KEY_ERROR_MESSAGE 
com.a2ia.data  KEY_HAS_SIGNATURE 
com.a2ia.data  KEY_HAS_VALID_CHEQUE_CONTOUR 
com.a2ia.data  KEY_IS_GENUINE 
com.a2ia.data  KEY_IS_SUCCESSFUL 
com.a2ia.data  
KEY_MICR_CODE 
com.a2ia.data  
KEY_MICR_RECT 
com.a2ia.data  KEY_MICR_SCORE 
com.a2ia.data  KEY_ORIGINAL_IMAGE 
com.a2ia.data  KEY_PROCESSED_IMAGE 
com.a2ia.data  KEY_SIGNATURE_RECT 
com.a2ia.data  KEY_SIGNATURE_SCORE 
com.a2ia.data  KEY_VALID_MICR_POSITION 
com.a2ia.data  List 
com.a2ia.data  Locale 
com.a2ia.data  Log 
com.a2ia.data  	LongArray 
com.a2ia.data  Map 
com.a2ia.data  Mat 
com.a2ia.data  MatOfInt 
com.a2ia.data  
MatOfPoint 
com.a2ia.data  MatOfPoint2f 
com.a2ia.data  Math 
com.a2ia.data  Matrix 
com.a2ia.data  MemoryImage 
com.a2ia.data  ModelV2 
com.a2ia.data  MutableList 
com.a2ia.data  
MutableMap 
com.a2ia.data  OnnxModelHandler 
com.a2ia.data  
OnnxTensor 
com.a2ia.data  OpenCVLoader 
com.a2ia.data  
OpenCVRect 
com.a2ia.data  OrtEnvironment 
com.a2ia.data  OutOfMemoryError 
com.a2ia.data  OutputFormat 
com.a2ia.data  Paint 
com.a2ia.data  Pair 
com.a2ia.data  Point 
com.a2ia.data  ProcessedImage 
com.a2ia.data  
ProcessorLogs 
com.a2ia.data  Random 
com.a2ia.data  Rect 
com.a2ia.data  Result 
com.a2ia.data  RuntimeException 
com.a2ia.data  SEGMENTATION_TIMEOUT_SECONDS 
com.a2ia.data  Scalar 
com.a2ia.data  SimpleDateFormat 
com.a2ia.data  Size 
com.a2ia.data  String 
com.a2ia.data  
StringBuilder 
com.a2ia.data  SubjectSegmentation 
com.a2ia.data  SubjectSegmenterOptions 
com.a2ia.data  System 
com.a2ia.data  TAG 
com.a2ia.data  
TFLiteHandler 
com.a2ia.data  Tasks 
com.a2ia.data  TensorBuffer 
com.a2ia.data  TextRecognition 
com.a2ia.data  TextRecognizerOptions 
com.a2ia.data  TimeUnit 
com.a2ia.data  Triple 
com.a2ia.data  Unit 
com.a2ia.data  Utils 
com.a2ia.data  VerboseDetails 
com.a2ia.data  YOLOModelHandler 
com.a2ia.data  also 
com.a2ia.data  android 
com.a2ia.data  apply 
com.a2ia.data  arrayOf 
com.a2ia.data  atan2 
com.a2ia.data  average 
com.a2ia.data  class_names 
com.a2ia.data  coerceIn 
com.a2ia.data  	compareBy 
com.a2ia.data  
component1 
com.a2ia.data  
component2 
com.a2ia.data  	divAssign 
com.a2ia.data  
doubleArrayOf 
com.a2ia.data  	emptyList 
com.a2ia.data  filter 
com.a2ia.data  floatArrayOf 
com.a2ia.data  forEach 
com.a2ia.data  forEachIndexed 
com.a2ia.data  format 
com.a2ia.data  	getOrNull 
com.a2ia.data  indices 
com.a2ia.data  instance 
com.a2ia.data  
intArrayOf 
com.a2ia.data  invoke 
com.a2ia.data  isDigit 
com.a2ia.data  
isNotEmpty 
com.a2ia.data  java 
com.a2ia.data  	javaClass 
com.a2ia.data  kotlin 
com.a2ia.data  last 
com.a2ia.data  let 
com.a2ia.data  listOf 
com.a2ia.data  longArrayOf 
com.a2ia.data  map 
com.a2ia.data  mapOf 
com.a2ia.data  max 
com.a2ia.data  maxOf 
com.a2ia.data  	maxOrNull 
com.a2ia.data  minOf 
com.a2ia.data  	minOrNull 
com.a2ia.data  
mutableListOf 
com.a2ia.data  mutableMapOf 
com.a2ia.data  
plusAssign 
com.a2ia.data  pow 
com.a2ia.data  println 
com.a2ia.data  result 
com.a2ia.data  run 
com.a2ia.data  set 
com.a2ia.data  sortedBy 
com.a2ia.data  sortedByDescending 
com.a2ia.data  
sortedWith 
com.a2ia.data  sqrt 
com.a2ia.data  stackTraceToString 
com.a2ia.data  	substring 
com.a2ia.data  take 
com.a2ia.data  thenBy 
com.a2ia.data  to 
com.a2ia.data  toTypedArray 
com.a2ia.data  
trimIndent 
com.a2ia.data  until 
com.a2ia.data  use 
com.a2ia.data  	withIndex 
com.a2ia.data  Input com.a2ia.data.A2iABoolean  No com.a2ia.data.A2iABoolean  AddressLineItem "com.a2ia.data.CheckDocumentResults  Amount "com.a2ia.data.CheckDocumentResults  Any "com.a2ia.data.CheckDocumentResults  Bitmap "com.a2ia.data.CheckDocumentResults  Boolean "com.a2ia.data.CheckDocumentResults  CARItem "com.a2ia.data.CheckDocumentResults  Field "com.a2ia.data.CheckDocumentResults  Float "com.a2ia.data.CheckDocumentResults  HashMap "com.a2ia.data.CheckDocumentResults  Int "com.a2ia.data.CheckDocumentResults  
Invalidity "com.a2ia.data.CheckDocumentResults  KEY_CVN_CODE "com.a2ia.data.CheckDocumentResults  KEY_CVN_RECT "com.a2ia.data.CheckDocumentResults  
KEY_CVN_SCORE "com.a2ia.data.CheckDocumentResults  KEY_ERROR_MESSAGE "com.a2ia.data.CheckDocumentResults  KEY_HAS_SIGNATURE "com.a2ia.data.CheckDocumentResults  KEY_HAS_VALID_CHEQUE_CONTOUR "com.a2ia.data.CheckDocumentResults  KEY_IS_GENUINE "com.a2ia.data.CheckDocumentResults  KEY_IS_SUCCESSFUL "com.a2ia.data.CheckDocumentResults  
KEY_MICR_CODE "com.a2ia.data.CheckDocumentResults  
KEY_MICR_RECT "com.a2ia.data.CheckDocumentResults  KEY_MICR_SCORE "com.a2ia.data.CheckDocumentResults  KEY_ORIGINAL_IMAGE "com.a2ia.data.CheckDocumentResults  KEY_PROCESSED_IMAGE "com.a2ia.data.CheckDocumentResults  KEY_SIGNATURE_RECT "com.a2ia.data.CheckDocumentResults  KEY_SIGNATURE_SCORE "com.a2ia.data.CheckDocumentResults  KEY_VALID_MICR_POSITION "com.a2ia.data.CheckDocumentResults  Map "com.a2ia.data.CheckDocumentResults  MutableList "com.a2ia.data.CheckDocumentResults  
MutableMap "com.a2ia.data.CheckDocumentResults  Rect "com.a2ia.data.CheckDocumentResults  Result "com.a2ia.data.CheckDocumentResults  String "com.a2ia.data.CheckDocumentResults  address "com.a2ia.data.CheckDocumentResults  addressLines "com.a2ia.data.CheckDocumentResults  apply "com.a2ia.data.CheckDocumentResults  carItems "com.a2ia.data.CheckDocumentResults  checkNumber "com.a2ia.data.CheckDocumentResults  codeline "com.a2ia.data.CheckDocumentResults  date "com.a2ia.data.CheckDocumentResults  getAPPLY "com.a2ia.data.CheckDocumentResults  getApply "com.a2ia.data.CheckDocumentResults  getGETOrNull "com.a2ia.data.CheckDocumentResults  getGetOrNull "com.a2ia.data.CheckDocumentResults  getHasValidChequeContour "com.a2ia.data.CheckDocumentResults  getMUTABLEListOf "com.a2ia.data.CheckDocumentResults  getMutableListOf "com.a2ia.data.CheckDocumentResults  	getOrNull "com.a2ia.data.CheckDocumentResults  getSET "com.a2ia.data.CheckDocumentResults  getSet "com.a2ia.data.CheckDocumentResults  getValidMicrPosition "com.a2ia.data.CheckDocumentResults  
invalidity "com.a2ia.data.CheckDocumentResults  
mutableListOf "com.a2ia.data.CheckDocumentResults  	payeeName "com.a2ia.data.CheckDocumentResults  result "com.a2ia.data.CheckDocumentResults  results "com.a2ia.data.CheckDocumentResults  
securityCode1 "com.a2ia.data.CheckDocumentResults  set "com.a2ia.data.CheckDocumentResults  
setCvnCode "com.a2ia.data.CheckDocumentResults  
setCvnRect "com.a2ia.data.CheckDocumentResults  setErrorMessage "com.a2ia.data.CheckDocumentResults  setHasSignature "com.a2ia.data.CheckDocumentResults  setIsGenuine "com.a2ia.data.CheckDocumentResults  setIsSuccessful "com.a2ia.data.CheckDocumentResults  setMicrCode "com.a2ia.data.CheckDocumentResults  setMicrRect "com.a2ia.data.CheckDocumentResults  setOriginalImage "com.a2ia.data.CheckDocumentResults  setProcessedImage "com.a2ia.data.CheckDocumentResults  setSignatureRect "com.a2ia.data.CheckDocumentResults  Int 2com.a2ia.data.CheckDocumentResults.AddressLineItem  String 2com.a2ia.data.CheckDocumentResults.AddressLineItem  apply 2com.a2ia.data.CheckDocumentResults.AddressLineItem  getAPPLY 2com.a2ia.data.CheckDocumentResults.AddressLineItem  getApply 2com.a2ia.data.CheckDocumentResults.AddressLineItem  	getRESULT 2com.a2ia.data.CheckDocumentResults.AddressLineItem  	getResult 2com.a2ia.data.CheckDocumentResults.AddressLineItem  reco 2com.a2ia.data.CheckDocumentResults.AddressLineItem  result 2com.a2ia.data.CheckDocumentResults.AddressLineItem  score 2com.a2ia.data.CheckDocumentResults.AddressLineItem  Int )com.a2ia.data.CheckDocumentResults.Amount  amount )com.a2ia.data.CheckDocumentResults.Amount  score )com.a2ia.data.CheckDocumentResults.Amount  Float *com.a2ia.data.CheckDocumentResults.CARItem  Int *com.a2ia.data.CheckDocumentResults.CARItem  amount *com.a2ia.data.CheckDocumentResults.CARItem  apply *com.a2ia.data.CheckDocumentResults.CARItem  getAPPLY *com.a2ia.data.CheckDocumentResults.CARItem  getApply *com.a2ia.data.CheckDocumentResults.CARItem  prob *com.a2ia.data.CheckDocumentResults.CARItem  AddressLineItem ,com.a2ia.data.CheckDocumentResults.Companion  Any ,com.a2ia.data.CheckDocumentResults.Companion  Bitmap ,com.a2ia.data.CheckDocumentResults.Companion  Boolean ,com.a2ia.data.CheckDocumentResults.Companion  Float ,com.a2ia.data.CheckDocumentResults.Companion  HashMap ,com.a2ia.data.CheckDocumentResults.Companion  Int ,com.a2ia.data.CheckDocumentResults.Companion  KEY_CVN_CODE ,com.a2ia.data.CheckDocumentResults.Companion  KEY_CVN_RECT ,com.a2ia.data.CheckDocumentResults.Companion  
KEY_CVN_SCORE ,com.a2ia.data.CheckDocumentResults.Companion  KEY_ERROR_MESSAGE ,com.a2ia.data.CheckDocumentResults.Companion  KEY_HAS_SIGNATURE ,com.a2ia.data.CheckDocumentResults.Companion  KEY_HAS_VALID_CHEQUE_CONTOUR ,com.a2ia.data.CheckDocumentResults.Companion  KEY_IS_GENUINE ,com.a2ia.data.CheckDocumentResults.Companion  KEY_IS_SUCCESSFUL ,com.a2ia.data.CheckDocumentResults.Companion  
KEY_MICR_CODE ,com.a2ia.data.CheckDocumentResults.Companion  
KEY_MICR_RECT ,com.a2ia.data.CheckDocumentResults.Companion  KEY_MICR_SCORE ,com.a2ia.data.CheckDocumentResults.Companion  KEY_ORIGINAL_IMAGE ,com.a2ia.data.CheckDocumentResults.Companion  KEY_PROCESSED_IMAGE ,com.a2ia.data.CheckDocumentResults.Companion  KEY_SIGNATURE_RECT ,com.a2ia.data.CheckDocumentResults.Companion  KEY_SIGNATURE_SCORE ,com.a2ia.data.CheckDocumentResults.Companion  KEY_VALID_MICR_POSITION ,com.a2ia.data.CheckDocumentResults.Companion  Map ,com.a2ia.data.CheckDocumentResults.Companion  MutableList ,com.a2ia.data.CheckDocumentResults.Companion  
MutableMap ,com.a2ia.data.CheckDocumentResults.Companion  Rect ,com.a2ia.data.CheckDocumentResults.Companion  Result ,com.a2ia.data.CheckDocumentResults.Companion  String ,com.a2ia.data.CheckDocumentResults.Companion  apply ,com.a2ia.data.CheckDocumentResults.Companion  getAPPLY ,com.a2ia.data.CheckDocumentResults.Companion  getApply ,com.a2ia.data.CheckDocumentResults.Companion  getGETOrNull ,com.a2ia.data.CheckDocumentResults.Companion  getGetOrNull ,com.a2ia.data.CheckDocumentResults.Companion  getMUTABLEListOf ,com.a2ia.data.CheckDocumentResults.Companion  getMutableListOf ,com.a2ia.data.CheckDocumentResults.Companion  	getOrNull ,com.a2ia.data.CheckDocumentResults.Companion  getSET ,com.a2ia.data.CheckDocumentResults.Companion  getSet ,com.a2ia.data.CheckDocumentResults.Companion  invoke ,com.a2ia.data.CheckDocumentResults.Companion  
mutableListOf ,com.a2ia.data.CheckDocumentResults.Companion  result ,com.a2ia.data.CheckDocumentResults.Companion  set ,com.a2ia.data.CheckDocumentResults.Companion  String -com.a2ia.data.CheckDocumentResults.DateResult  AddressLineItem (com.a2ia.data.CheckDocumentResults.Field  Int (com.a2ia.data.CheckDocumentResults.Field  Result (com.a2ia.data.CheckDocumentResults.Field  apply (com.a2ia.data.CheckDocumentResults.Field  getAPPLY (com.a2ia.data.CheckDocumentResults.Field  getApply (com.a2ia.data.CheckDocumentResults.Field  result (com.a2ia.data.CheckDocumentResults.Field  Boolean -com.a2ia.data.CheckDocumentResults.Invalidity  Int -com.a2ia.data.CheckDocumentResults.Invalidity  noSignature -com.a2ia.data.CheckDocumentResults.Invalidity  noSignatureScore -com.a2ia.data.CheckDocumentResults.Invalidity  Int )com.a2ia.data.CheckDocumentResults.Result  String )com.a2ia.data.CheckDocumentResults.Result  reco )com.a2ia.data.CheckDocumentResults.Result  score )com.a2ia.data.CheckDocumentResults.Result  Bitmap com.a2ia.data.CheckInput  
BitmapFactory com.a2ia.data.CheckInput  CheckDocumentResults com.a2ia.data.CheckInput  
CheckInput com.a2ia.data.CheckInput  ChequeProcessorSDK com.a2ia.data.CheckInput  Context com.a2ia.data.CheckInput  	Exception com.a2ia.data.CheckInput  ExecutorService com.a2ia.data.CheckInput  	Executors com.a2ia.data.CheckInput  File com.a2ia.data.CheckInput  InputStream com.a2ia.data.CheckInput  	JvmStatic com.a2ia.data.CheckInput  Log com.a2ia.data.CheckInput  String com.a2ia.data.CheckInput  TAG com.a2ia.data.CheckInput  Unit com.a2ia.data.CheckInput  context com.a2ia.data.CheckInput  equals com.a2ia.data.CheckInput  executorService com.a2ia.data.CheckInput  getINSTANCE com.a2ia.data.CheckInput  getInstance com.a2ia.data.CheckInput  instance com.a2ia.data.CheckInput  invoke com.a2ia.data.CheckInput  loadBitmapFromFile com.a2ia.data.CheckInput  processImage com.a2ia.data.CheckInput  processImageBitmap com.a2ia.data.CheckInput  Bitmap "com.a2ia.data.CheckInput.Companion  
BitmapFactory "com.a2ia.data.CheckInput.Companion  CheckDocumentResults "com.a2ia.data.CheckInput.Companion  
CheckInput "com.a2ia.data.CheckInput.Companion  ChequeProcessorSDK "com.a2ia.data.CheckInput.Companion  Context "com.a2ia.data.CheckInput.Companion  	Exception "com.a2ia.data.CheckInput.Companion  ExecutorService "com.a2ia.data.CheckInput.Companion  	Executors "com.a2ia.data.CheckInput.Companion  File "com.a2ia.data.CheckInput.Companion  InputStream "com.a2ia.data.CheckInput.Companion  	JvmStatic "com.a2ia.data.CheckInput.Companion  Log "com.a2ia.data.CheckInput.Companion  String "com.a2ia.data.CheckInput.Companion  TAG "com.a2ia.data.CheckInput.Companion  Unit "com.a2ia.data.CheckInput.Companion  instance "com.a2ia.data.CheckInput.Companion  invoke "com.a2ia.data.CheckInput.Companion  Array  com.a2ia.data.ChequeProcessorSDK  	ArrayList  com.a2ia.data.ChequeProcessorSDK  AtomicReference  com.a2ia.data.ChequeProcessorSDK  Bitmap  com.a2ia.data.ChequeProcessorSDK  Boolean  com.a2ia.data.ChequeProcessorSDK  Canvas  com.a2ia.data.ChequeProcessorSDK  ChequeProcessorSDK  com.a2ia.data.ChequeProcessorSDK  ChequeUtils  com.a2ia.data.ChequeProcessorSDK  ClockwiseComparator  com.a2ia.data.ChequeProcessorSDK  Collections  com.a2ia.data.ChequeProcessorSDK  Color  com.a2ia.data.ChequeProcessorSDK  
Comparator  com.a2ia.data.ChequeProcessorSDK  Context  com.a2ia.data.ChequeProcessorSDK  Core  com.a2ia.data.ChequeProcessorSDK  CountDownLatch  com.a2ia.data.ChequeProcessorSDK  CvType  com.a2ia.data.ChequeProcessorSDK  Date  com.a2ia.data.ChequeProcessorSDK  Double  com.a2ia.data.ChequeProcessorSDK  DoubleArray  com.a2ia.data.ChequeProcessorSDK  	Exception  com.a2ia.data.ChequeProcessorSDK  	Executors  com.a2ia.data.ChequeProcessorSDK  File  com.a2ia.data.ChequeProcessorSDK  FileOutputStream  com.a2ia.data.ChequeProcessorSDK  HashMap  com.a2ia.data.ChequeProcessorSDK  IllegalStateException  com.a2ia.data.ChequeProcessorSDK  Imgproc  com.a2ia.data.ChequeProcessorSDK  
InputImage  com.a2ia.data.ChequeProcessorSDK  Int  com.a2ia.data.ChequeProcessorSDK  IntArray  com.a2ia.data.ChequeProcessorSDK  JvmField  com.a2ia.data.ChequeProcessorSDK  	JvmStatic  com.a2ia.data.ChequeProcessorSDK  List  com.a2ia.data.ChequeProcessorSDK  Locale  com.a2ia.data.ChequeProcessorSDK  Log  com.a2ia.data.ChequeProcessorSDK  	MICRCodes  com.a2ia.data.ChequeProcessorSDK  Mat  com.a2ia.data.ChequeProcessorSDK  MatOfInt  com.a2ia.data.ChequeProcessorSDK  
MatOfPoint  com.a2ia.data.ChequeProcessorSDK  MatOfPoint2f  com.a2ia.data.ChequeProcessorSDK  Math  com.a2ia.data.ChequeProcessorSDK  Matrix  com.a2ia.data.ChequeProcessorSDK  MutableList  com.a2ia.data.ChequeProcessorSDK  OnnxModelHandler  com.a2ia.data.ChequeProcessorSDK  OpenCVLoader  com.a2ia.data.ChequeProcessorSDK  
OpenCVRect  com.a2ia.data.ChequeProcessorSDK  Paint  com.a2ia.data.ChequeProcessorSDK  Pair  com.a2ia.data.ChequeProcessorSDK  Point  com.a2ia.data.ChequeProcessorSDK  
ProcessorLogs  com.a2ia.data.ChequeProcessorSDK  Rect  com.a2ia.data.ChequeProcessorSDK  RotatedRect  com.a2ia.data.ChequeProcessorSDK  SEGMENTATION_TIMEOUT_SECONDS  com.a2ia.data.ChequeProcessorSDK  Scalar  com.a2ia.data.ChequeProcessorSDK  SimpleDateFormat  com.a2ia.data.ChequeProcessorSDK  Size  com.a2ia.data.ChequeProcessorSDK  String  com.a2ia.data.ChequeProcessorSDK  SubjectSegmentation  com.a2ia.data.ChequeProcessorSDK  SubjectSegmenter  com.a2ia.data.ChequeProcessorSDK  SubjectSegmenterOptions  com.a2ia.data.ChequeProcessorSDK  System  com.a2ia.data.ChequeProcessorSDK  TAG  com.a2ia.data.ChequeProcessorSDK  
TFLiteHandler  com.a2ia.data.ChequeProcessorSDK  Tasks  com.a2ia.data.ChequeProcessorSDK  Text  com.a2ia.data.ChequeProcessorSDK  TextRecognition  com.a2ia.data.ChequeProcessorSDK  TextRecognizer  com.a2ia.data.ChequeProcessorSDK  TextRecognizerOptions  com.a2ia.data.ChequeProcessorSDK  TimeUnit  com.a2ia.data.ChequeProcessorSDK  Utils  com.a2ia.data.ChequeProcessorSDK  YOLOModelHandler  com.a2ia.data.ChequeProcessorSDK  apply  com.a2ia.data.ChequeProcessorSDK  arrayOf  com.a2ia.data.ChequeProcessorSDK  average  com.a2ia.data.ChequeProcessorSDK  calculateContoursScore  com.a2ia.data.ChequeProcessorSDK  calculatePixelDistributionScore  com.a2ia.data.ChequeProcessorSDK  calculateStrokeScore  com.a2ia.data.ChequeProcessorSDK  	clampRect  com.a2ia.data.ChequeProcessorSDK  clearPreviousResults  com.a2ia.data.ChequeProcessorSDK  coerceIn  com.a2ia.data.ChequeProcessorSDK  	compareBy  com.a2ia.data.ChequeProcessorSDK  context  com.a2ia.data.ChequeProcessorSDK  cropCnt  com.a2ia.data.ChequeProcessorSDK  cvnCode  com.a2ia.data.ChequeProcessorSDK  cvnRect  com.a2ia.data.ChequeProcessorSDK  	cvnRegion  com.a2ia.data.ChequeProcessorSDK  cvnScore  com.a2ia.data.ChequeProcessorSDK  detectSignature  com.a2ia.data.ChequeProcessorSDK  
doubleArrayOf  com.a2ia.data.ChequeProcessorSDK  equals  com.a2ia.data.ChequeProcessorSDK  extractContours  com.a2ia.data.ChequeProcessorSDK  extractDigitsAndSymbols  com.a2ia.data.ChequeProcessorSDK  filter  com.a2ia.data.ChequeProcessorSDK  forEachIndexed  com.a2ia.data.ChequeProcessorSDK  format  com.a2ia.data.ChequeProcessorSDK  getAPPLY  com.a2ia.data.ChequeProcessorSDK  
getARRAYOf  com.a2ia.data.ChequeProcessorSDK  
getAVERAGE  com.a2ia.data.ChequeProcessorSDK  getApply  com.a2ia.data.ChequeProcessorSDK  
getArrayOf  com.a2ia.data.ChequeProcessorSDK  
getAverage  com.a2ia.data.ChequeProcessorSDK  getCOERCEIn  com.a2ia.data.ChequeProcessorSDK  getCOMPAREBy  com.a2ia.data.ChequeProcessorSDK  getCoerceIn  com.a2ia.data.ChequeProcessorSDK  getCompareBy  com.a2ia.data.ChequeProcessorSDK  getDOUBLEArrayOf  com.a2ia.data.ChequeProcessorSDK  getDetectedSignatureRegion  com.a2ia.data.ChequeProcessorSDK  getDoubleArrayOf  com.a2ia.data.ChequeProcessorSDK  	getFILTER  com.a2ia.data.ChequeProcessorSDK  getFOREachIndexed  com.a2ia.data.ChequeProcessorSDK  	getFORMAT  com.a2ia.data.ChequeProcessorSDK  	getFilter  com.a2ia.data.ChequeProcessorSDK  getForEachIndexed  com.a2ia.data.ChequeProcessorSDK  	getFormat  com.a2ia.data.ChequeProcessorSDK  getHasValidChequeContour  com.a2ia.data.ChequeProcessorSDK  getINSTANCE  com.a2ia.data.ChequeProcessorSDK  
getINTArrayOf  com.a2ia.data.ChequeProcessorSDK  
getISDigit  com.a2ia.data.ChequeProcessorSDK  
getISNotEmpty  com.a2ia.data.ChequeProcessorSDK  getInstance  com.a2ia.data.ChequeProcessorSDK  
getIntArrayOf  com.a2ia.data.ChequeProcessorSDK  
getIsDigit  com.a2ia.data.ChequeProcessorSDK  
getIsNotEmpty  com.a2ia.data.ChequeProcessorSDK  getJAVA  com.a2ia.data.ChequeProcessorSDK  getJava  com.a2ia.data.ChequeProcessorSDK  getLAST  com.a2ia.data.ChequeProcessorSDK  getLET  com.a2ia.data.ChequeProcessorSDK  	getLISTOf  com.a2ia.data.ChequeProcessorSDK  getLast  com.a2ia.data.ChequeProcessorSDK  getLet  com.a2ia.data.ChequeProcessorSDK  	getListOf  com.a2ia.data.ChequeProcessorSDK  getMAP  com.a2ia.data.ChequeProcessorSDK  getMAX  com.a2ia.data.ChequeProcessorSDK  getMAXOf  com.a2ia.data.ChequeProcessorSDK  getMINOf  com.a2ia.data.ChequeProcessorSDK  getMap  com.a2ia.data.ChequeProcessorSDK  getMax  com.a2ia.data.ChequeProcessorSDK  getMaxOf  com.a2ia.data.ChequeProcessorSDK  getMinOf  com.a2ia.data.ChequeProcessorSDK  
getPLUSAssign  com.a2ia.data.ChequeProcessorSDK  
getPRINTLN  com.a2ia.data.ChequeProcessorSDK  
getPlusAssign  com.a2ia.data.ChequeProcessorSDK  
getPrintln  com.a2ia.data.ChequeProcessorSDK  getSORTEDBy  com.a2ia.data.ChequeProcessorSDK  
getSORTEDWith  com.a2ia.data.ChequeProcessorSDK  getSUBSTRING  com.a2ia.data.ChequeProcessorSDK  getSortedBy  com.a2ia.data.ChequeProcessorSDK  
getSortedWith  com.a2ia.data.ChequeProcessorSDK  getSubstring  com.a2ia.data.ChequeProcessorSDK  getTAKE  com.a2ia.data.ChequeProcessorSDK  	getTHENBy  com.a2ia.data.ChequeProcessorSDK  getTOTypedArray  com.a2ia.data.ChequeProcessorSDK  getTake  com.a2ia.data.ChequeProcessorSDK  	getThenBy  com.a2ia.data.ChequeProcessorSDK  getToTypedArray  com.a2ia.data.ChequeProcessorSDK  getUNTIL  com.a2ia.data.ChequeProcessorSDK  getUSE  com.a2ia.data.ChequeProcessorSDK  getUntil  com.a2ia.data.ChequeProcessorSDK  getUse  com.a2ia.data.ChequeProcessorSDK  getValidMicrPosition  com.a2ia.data.ChequeProcessorSDK  getWITHIndex  com.a2ia.data.ChequeProcessorSDK  getWithIndex  com.a2ia.data.ChequeProcessorSDK  hasSignature  com.a2ia.data.ChequeProcessorSDK  hasValidChequeContour  com.a2ia.data.ChequeProcessorSDK  identifyChequeType  com.a2ia.data.ChequeProcessorSDK  indices  com.a2ia.data.ChequeProcessorSDK  instance  com.a2ia.data.ChequeProcessorSDK  
intArrayOf  com.a2ia.data.ChequeProcessorSDK  invoke  com.a2ia.data.ChequeProcessorSDK  isDigit  com.a2ia.data.ChequeProcessorSDK  	isGenuine  com.a2ia.data.ChequeProcessorSDK  isGenuineCheque  com.a2ia.data.ChequeProcessorSDK  
isNotEmpty  com.a2ia.data.ChequeProcessorSDK  java  com.a2ia.data.ChequeProcessorSDK  last  com.a2ia.data.ChequeProcessorSDK  let  com.a2ia.data.ChequeProcessorSDK  lineBoundingBoxes  com.a2ia.data.ChequeProcessorSDK  listOf  com.a2ia.data.ChequeProcessorSDK  map  com.a2ia.data.ChequeProcessorSDK  max  com.a2ia.data.ChequeProcessorSDK  maxOf  com.a2ia.data.ChequeProcessorSDK  micrCode  com.a2ia.data.ChequeProcessorSDK  micrRect  com.a2ia.data.ChequeProcessorSDK  
micrRegion  com.a2ia.data.ChequeProcessorSDK  	micrScore  com.a2ia.data.ChequeProcessorSDK  minOf  com.a2ia.data.ChequeProcessorSDK  onnxModelHandler  com.a2ia.data.ChequeProcessorSDK  originalBitmap  com.a2ia.data.ChequeProcessorSDK  
padCharImg  com.a2ia.data.ChequeProcessorSDK  
plusAssign  com.a2ia.data.ChequeProcessorSDK  preProcessFrame  com.a2ia.data.ChequeProcessorSDK  println  com.a2ia.data.ChequeProcessorSDK  processCVNRegion  com.a2ia.data.ChequeProcessorSDK  processChequeImage  com.a2ia.data.ChequeProcessorSDK  processMICRRegion  com.a2ia.data.ChequeProcessorSDK  processTextRecognition  com.a2ia.data.ChequeProcessorSDK  processedBitmap  com.a2ia.data.ChequeProcessorSDK  processedMat  com.a2ia.data.ChequeProcessorSDK  recognizeCVNCode  com.a2ia.data.ChequeProcessorSDK  recognizeMICRCode  com.a2ia.data.ChequeProcessorSDK  scaleBitmapIfNeeded  com.a2ia.data.ChequeProcessorSDK  segmentedBitmap  com.a2ia.data.ChequeProcessorSDK  selectChequeContour  com.a2ia.data.ChequeProcessorSDK  
signatureRect  com.a2ia.data.ChequeProcessorSDK  signatureRegion  com.a2ia.data.ChequeProcessorSDK  signatureScore  com.a2ia.data.ChequeProcessorSDK  sortedBy  com.a2ia.data.ChequeProcessorSDK  
sortedWith  com.a2ia.data.ChequeProcessorSDK  	substring  com.a2ia.data.ChequeProcessorSDK  take  com.a2ia.data.ChequeProcessorSDK  
tfLiteHandler  com.a2ia.data.ChequeProcessorSDK  thenBy  com.a2ia.data.ChequeProcessorSDK  toTypedArray  com.a2ia.data.ChequeProcessorSDK  until  com.a2ia.data.ChequeProcessorSDK  use  com.a2ia.data.ChequeProcessorSDK  validMicrPosition  com.a2ia.data.ChequeProcessorSDK  winLen  com.a2ia.data.ChequeProcessorSDK  	withIndex  com.a2ia.data.ChequeProcessorSDK  yoloModelHandler  com.a2ia.data.ChequeProcessorSDK  Array *com.a2ia.data.ChequeProcessorSDK.Companion  	ArrayList *com.a2ia.data.ChequeProcessorSDK.Companion  AtomicReference *com.a2ia.data.ChequeProcessorSDK.Companion  Bitmap *com.a2ia.data.ChequeProcessorSDK.Companion  Boolean *com.a2ia.data.ChequeProcessorSDK.Companion  Canvas *com.a2ia.data.ChequeProcessorSDK.Companion  ChequeProcessorSDK *com.a2ia.data.ChequeProcessorSDK.Companion  ChequeUtils *com.a2ia.data.ChequeProcessorSDK.Companion  ClockwiseComparator *com.a2ia.data.ChequeProcessorSDK.Companion  Collections *com.a2ia.data.ChequeProcessorSDK.Companion  Color *com.a2ia.data.ChequeProcessorSDK.Companion  
Comparator *com.a2ia.data.ChequeProcessorSDK.Companion  Context *com.a2ia.data.ChequeProcessorSDK.Companion  Core *com.a2ia.data.ChequeProcessorSDK.Companion  CountDownLatch *com.a2ia.data.ChequeProcessorSDK.Companion  CvType *com.a2ia.data.ChequeProcessorSDK.Companion  Date *com.a2ia.data.ChequeProcessorSDK.Companion  Double *com.a2ia.data.ChequeProcessorSDK.Companion  DoubleArray *com.a2ia.data.ChequeProcessorSDK.Companion  	Exception *com.a2ia.data.ChequeProcessorSDK.Companion  	Executors *com.a2ia.data.ChequeProcessorSDK.Companion  File *com.a2ia.data.ChequeProcessorSDK.Companion  FileOutputStream *com.a2ia.data.ChequeProcessorSDK.Companion  HashMap *com.a2ia.data.ChequeProcessorSDK.Companion  IllegalStateException *com.a2ia.data.ChequeProcessorSDK.Companion  Imgproc *com.a2ia.data.ChequeProcessorSDK.Companion  
InputImage *com.a2ia.data.ChequeProcessorSDK.Companion  Int *com.a2ia.data.ChequeProcessorSDK.Companion  IntArray *com.a2ia.data.ChequeProcessorSDK.Companion  JvmField *com.a2ia.data.ChequeProcessorSDK.Companion  	JvmStatic *com.a2ia.data.ChequeProcessorSDK.Companion  List *com.a2ia.data.ChequeProcessorSDK.Companion  Locale *com.a2ia.data.ChequeProcessorSDK.Companion  Log *com.a2ia.data.ChequeProcessorSDK.Companion  Mat *com.a2ia.data.ChequeProcessorSDK.Companion  MatOfInt *com.a2ia.data.ChequeProcessorSDK.Companion  
MatOfPoint *com.a2ia.data.ChequeProcessorSDK.Companion  MatOfPoint2f *com.a2ia.data.ChequeProcessorSDK.Companion  Math *com.a2ia.data.ChequeProcessorSDK.Companion  Matrix *com.a2ia.data.ChequeProcessorSDK.Companion  MutableList *com.a2ia.data.ChequeProcessorSDK.Companion  OnnxModelHandler *com.a2ia.data.ChequeProcessorSDK.Companion  OpenCVLoader *com.a2ia.data.ChequeProcessorSDK.Companion  
OpenCVRect *com.a2ia.data.ChequeProcessorSDK.Companion  Paint *com.a2ia.data.ChequeProcessorSDK.Companion  Pair *com.a2ia.data.ChequeProcessorSDK.Companion  Point *com.a2ia.data.ChequeProcessorSDK.Companion  
ProcessorLogs *com.a2ia.data.ChequeProcessorSDK.Companion  Rect *com.a2ia.data.ChequeProcessorSDK.Companion  RotatedRect *com.a2ia.data.ChequeProcessorSDK.Companion  SEGMENTATION_TIMEOUT_SECONDS *com.a2ia.data.ChequeProcessorSDK.Companion  Scalar *com.a2ia.data.ChequeProcessorSDK.Companion  SimpleDateFormat *com.a2ia.data.ChequeProcessorSDK.Companion  Size *com.a2ia.data.ChequeProcessorSDK.Companion  String *com.a2ia.data.ChequeProcessorSDK.Companion  SubjectSegmentation *com.a2ia.data.ChequeProcessorSDK.Companion  SubjectSegmenter *com.a2ia.data.ChequeProcessorSDK.Companion  SubjectSegmenterOptions *com.a2ia.data.ChequeProcessorSDK.Companion  System *com.a2ia.data.ChequeProcessorSDK.Companion  TAG *com.a2ia.data.ChequeProcessorSDK.Companion  
TFLiteHandler *com.a2ia.data.ChequeProcessorSDK.Companion  Tasks *com.a2ia.data.ChequeProcessorSDK.Companion  Text *com.a2ia.data.ChequeProcessorSDK.Companion  TextRecognition *com.a2ia.data.ChequeProcessorSDK.Companion  TextRecognizer *com.a2ia.data.ChequeProcessorSDK.Companion  TextRecognizerOptions *com.a2ia.data.ChequeProcessorSDK.Companion  TimeUnit *com.a2ia.data.ChequeProcessorSDK.Companion  Utils *com.a2ia.data.ChequeProcessorSDK.Companion  YOLOModelHandler *com.a2ia.data.ChequeProcessorSDK.Companion  apply *com.a2ia.data.ChequeProcessorSDK.Companion  arrayOf *com.a2ia.data.ChequeProcessorSDK.Companion  average *com.a2ia.data.ChequeProcessorSDK.Companion  coerceIn *com.a2ia.data.ChequeProcessorSDK.Companion  	compareBy *com.a2ia.data.ChequeProcessorSDK.Companion  
doubleArrayOf *com.a2ia.data.ChequeProcessorSDK.Companion  filter *com.a2ia.data.ChequeProcessorSDK.Companion  forEachIndexed *com.a2ia.data.ChequeProcessorSDK.Companion  format *com.a2ia.data.ChequeProcessorSDK.Companion  getAPPLY *com.a2ia.data.ChequeProcessorSDK.Companion  
getARRAYOf *com.a2ia.data.ChequeProcessorSDK.Companion  
getAVERAGE *com.a2ia.data.ChequeProcessorSDK.Companion  getApply *com.a2ia.data.ChequeProcessorSDK.Companion  
getArrayOf *com.a2ia.data.ChequeProcessorSDK.Companion  
getAverage *com.a2ia.data.ChequeProcessorSDK.Companion  getCOERCEIn *com.a2ia.data.ChequeProcessorSDK.Companion  getCOMPAREBy *com.a2ia.data.ChequeProcessorSDK.Companion  getCoerceIn *com.a2ia.data.ChequeProcessorSDK.Companion  getCompareBy *com.a2ia.data.ChequeProcessorSDK.Companion  getDOUBLEArrayOf *com.a2ia.data.ChequeProcessorSDK.Companion  getDoubleArrayOf *com.a2ia.data.ChequeProcessorSDK.Companion  	getFILTER *com.a2ia.data.ChequeProcessorSDK.Companion  getFOREachIndexed *com.a2ia.data.ChequeProcessorSDK.Companion  	getFORMAT *com.a2ia.data.ChequeProcessorSDK.Companion  	getFilter *com.a2ia.data.ChequeProcessorSDK.Companion  getForEachIndexed *com.a2ia.data.ChequeProcessorSDK.Companion  	getFormat *com.a2ia.data.ChequeProcessorSDK.Companion  
getINTArrayOf *com.a2ia.data.ChequeProcessorSDK.Companion  
getISDigit *com.a2ia.data.ChequeProcessorSDK.Companion  
getISNotEmpty *com.a2ia.data.ChequeProcessorSDK.Companion  getInstance *com.a2ia.data.ChequeProcessorSDK.Companion  
getIntArrayOf *com.a2ia.data.ChequeProcessorSDK.Companion  
getIsDigit *com.a2ia.data.ChequeProcessorSDK.Companion  
getIsNotEmpty *com.a2ia.data.ChequeProcessorSDK.Companion  getJAVA *com.a2ia.data.ChequeProcessorSDK.Companion  getJava *com.a2ia.data.ChequeProcessorSDK.Companion  getLAST *com.a2ia.data.ChequeProcessorSDK.Companion  getLET *com.a2ia.data.ChequeProcessorSDK.Companion  	getLISTOf *com.a2ia.data.ChequeProcessorSDK.Companion  getLast *com.a2ia.data.ChequeProcessorSDK.Companion  getLet *com.a2ia.data.ChequeProcessorSDK.Companion  	getListOf *com.a2ia.data.ChequeProcessorSDK.Companion  getMAP *com.a2ia.data.ChequeProcessorSDK.Companion  getMAX *com.a2ia.data.ChequeProcessorSDK.Companion  getMAXOf *com.a2ia.data.ChequeProcessorSDK.Companion  getMINOf *com.a2ia.data.ChequeProcessorSDK.Companion  getMap *com.a2ia.data.ChequeProcessorSDK.Companion  getMax *com.a2ia.data.ChequeProcessorSDK.Companion  getMaxOf *com.a2ia.data.ChequeProcessorSDK.Companion  getMinOf *com.a2ia.data.ChequeProcessorSDK.Companion  
getPLUSAssign *com.a2ia.data.ChequeProcessorSDK.Companion  
getPRINTLN *com.a2ia.data.ChequeProcessorSDK.Companion  
getPlusAssign *com.a2ia.data.ChequeProcessorSDK.Companion  
getPrintln *com.a2ia.data.ChequeProcessorSDK.Companion  getSORTEDBy *com.a2ia.data.ChequeProcessorSDK.Companion  
getSORTEDWith *com.a2ia.data.ChequeProcessorSDK.Companion  getSUBSTRING *com.a2ia.data.ChequeProcessorSDK.Companion  getSortedBy *com.a2ia.data.ChequeProcessorSDK.Companion  
getSortedWith *com.a2ia.data.ChequeProcessorSDK.Companion  getSubstring *com.a2ia.data.ChequeProcessorSDK.Companion  getTAKE *com.a2ia.data.ChequeProcessorSDK.Companion  	getTHENBy *com.a2ia.data.ChequeProcessorSDK.Companion  getTOTypedArray *com.a2ia.data.ChequeProcessorSDK.Companion  getTake *com.a2ia.data.ChequeProcessorSDK.Companion  	getThenBy *com.a2ia.data.ChequeProcessorSDK.Companion  getToTypedArray *com.a2ia.data.ChequeProcessorSDK.Companion  getUNTIL *com.a2ia.data.ChequeProcessorSDK.Companion  getUSE *com.a2ia.data.ChequeProcessorSDK.Companion  getUntil *com.a2ia.data.ChequeProcessorSDK.Companion  getUse *com.a2ia.data.ChequeProcessorSDK.Companion  getWITHIndex *com.a2ia.data.ChequeProcessorSDK.Companion  getWithIndex *com.a2ia.data.ChequeProcessorSDK.Companion  indices *com.a2ia.data.ChequeProcessorSDK.Companion  init *com.a2ia.data.ChequeProcessorSDK.Companion  instance *com.a2ia.data.ChequeProcessorSDK.Companion  
intArrayOf *com.a2ia.data.ChequeProcessorSDK.Companion  invoke *com.a2ia.data.ChequeProcessorSDK.Companion  isDigit *com.a2ia.data.ChequeProcessorSDK.Companion  
isNotEmpty *com.a2ia.data.ChequeProcessorSDK.Companion  isProcessingCheque *com.a2ia.data.ChequeProcessorSDK.Companion  java *com.a2ia.data.ChequeProcessorSDK.Companion  last *com.a2ia.data.ChequeProcessorSDK.Companion  let *com.a2ia.data.ChequeProcessorSDK.Companion  listOf *com.a2ia.data.ChequeProcessorSDK.Companion  map *com.a2ia.data.ChequeProcessorSDK.Companion  max *com.a2ia.data.ChequeProcessorSDK.Companion  maxOf *com.a2ia.data.ChequeProcessorSDK.Companion  minOf *com.a2ia.data.ChequeProcessorSDK.Companion  
plusAssign *com.a2ia.data.ChequeProcessorSDK.Companion  println *com.a2ia.data.ChequeProcessorSDK.Companion  sortedBy *com.a2ia.data.ChequeProcessorSDK.Companion  
sortedWith *com.a2ia.data.ChequeProcessorSDK.Companion  	substring *com.a2ia.data.ChequeProcessorSDK.Companion  take *com.a2ia.data.ChequeProcessorSDK.Companion  thenBy *com.a2ia.data.ChequeProcessorSDK.Companion  toTypedArray *com.a2ia.data.ChequeProcessorSDK.Companion  until *com.a2ia.data.ChequeProcessorSDK.Companion  use *com.a2ia.data.ChequeProcessorSDK.Companion  	withIndex *com.a2ia.data.ChequeProcessorSDK.Companion  Bitmap com.a2ia.data.ChequeUtils  	ByteArray com.a2ia.data.ChequeUtils  	Exception com.a2ia.data.ChequeUtils  
FloatArray com.a2ia.data.ChequeUtils  Imgproc com.a2ia.data.ChequeUtils  Int com.a2ia.data.ChequeUtils  	JvmStatic com.a2ia.data.ChequeUtils  Log com.a2ia.data.ChequeUtils  Mat com.a2ia.data.ChequeUtils  Matrix com.a2ia.data.ChequeUtils  TAG com.a2ia.data.ChequeUtils  Utils com.a2ia.data.ChequeUtils  also com.a2ia.data.ChequeUtils  apply com.a2ia.data.ChequeUtils  convertBitmapToMat com.a2ia.data.ChequeUtils  convertMatToBitmap com.a2ia.data.ChequeUtils  floatArrayOf com.a2ia.data.ChequeUtils  forEachIndexed com.a2ia.data.ChequeUtils  getALSO com.a2ia.data.ChequeUtils  getAPPLY com.a2ia.data.ChequeUtils  getAlso com.a2ia.data.ChequeUtils  getApply com.a2ia.data.ChequeUtils  getFLOATArrayOf com.a2ia.data.ChequeUtils  getFOREachIndexed com.a2ia.data.ChequeUtils  getFloatArrayOf com.a2ia.data.ChequeUtils  getForEachIndexed com.a2ia.data.ChequeUtils  getLET com.a2ia.data.ChequeUtils  getLet com.a2ia.data.ChequeUtils  getRUN com.a2ia.data.ChequeUtils  getRun com.a2ia.data.ChequeUtils  let com.a2ia.data.ChequeUtils  rotateBitmap com.a2ia.data.ChequeUtils  run com.a2ia.data.ChequeUtils  Double !com.a2ia.data.ClockwiseComparator  Int !com.a2ia.data.ClockwiseComparator  Point !com.a2ia.data.ClockwiseComparator  atan2 !com.a2ia.data.ClockwiseComparator  calculateDistance !com.a2ia.data.ClockwiseComparator  center !com.a2ia.data.ClockwiseComparator  getAtan2 !com.a2ia.data.ClockwiseComparator  getPOW !com.a2ia.data.ClockwiseComparator  getPow !com.a2ia.data.ClockwiseComparator  getSQRT !com.a2ia.data.ClockwiseComparator  getSqrt !com.a2ia.data.ClockwiseComparator  pow !com.a2ia.data.ClockwiseComparator  sqrt !com.a2ia.data.ClockwiseComparator  Bitmap com.a2ia.data.ImageConverter  
BitmapFactory com.a2ia.data.ImageConverter  Boolean com.a2ia.data.ImageConverter  	ByteArray com.a2ia.data.ImageConverter  ByteArrayOutputStream com.a2ia.data.ImageConverter  OutputFormat com.a2ia.data.ImageConverter  android com.a2ia.data.ImageConverter  apply com.a2ia.data.ImageConverter  convertToGrayscale com.a2ia.data.ImageConverter  
convertToJPEG com.a2ia.data.ImageConverter  convertToPNG com.a2ia.data.ImageConverter  floatArrayOf com.a2ia.data.ImageConverter  
getANDROID com.a2ia.data.ImageConverter  getAPPLY com.a2ia.data.ImageConverter  
getAndroid com.a2ia.data.ImageConverter  getApply com.a2ia.data.ImageConverter  getFLOATArrayOf com.a2ia.data.ImageConverter  getFloatArrayOf com.a2ia.data.ImageConverter  reusableStream com.a2ia.data.ImageConverter  JPEG com.a2ia.data.ImageFormat  OutputFormat com.a2ia.data.ImageOutputFormat  outputFormat com.a2ia.data.ImageOutputFormat  A2iABoolean com.a2ia.data.Input  MemoryImage com.a2ia.data.Input  VerboseDetails com.a2ia.data.Input  verbose com.a2ia.data.Input  verboseDetails com.a2ia.data.Input  	ByteArray com.a2ia.data.MemoryImage  ImageFormat com.a2ia.data.MemoryImage  Bitmap com.a2ia.data.OnnxModelHandler  	ByteArray com.a2ia.data.OnnxModelHandler  Context com.a2ia.data.OnnxModelHandler  DEFAULT_MODEL_PATH com.a2ia.data.OnnxModelHandler  	Exception com.a2ia.data.OnnxModelHandler  
FloatArray com.a2ia.data.OnnxModelHandler  FloatBuffer com.a2ia.data.OnnxModelHandler  
INPUT_SIZE com.a2ia.data.OnnxModelHandler  Imgproc com.a2ia.data.OnnxModelHandler  Int com.a2ia.data.OnnxModelHandler  Log com.a2ia.data.OnnxModelHandler  	LongArray com.a2ia.data.OnnxModelHandler  Mat com.a2ia.data.OnnxModelHandler  
OnnxTensor com.a2ia.data.OnnxModelHandler  OrtEnvironment com.a2ia.data.OnnxModelHandler  OrtException com.a2ia.data.OnnxModelHandler  
OrtSession com.a2ia.data.OnnxModelHandler  RuntimeException com.a2ia.data.OnnxModelHandler  Size com.a2ia.data.OnnxModelHandler  String com.a2ia.data.OnnxModelHandler  TAG com.a2ia.data.OnnxModelHandler  Utils com.a2ia.data.OnnxModelHandler  close com.a2ia.data.OnnxModelHandler  context com.a2ia.data.OnnxModelHandler  convertMatToFloatArray com.a2ia.data.OnnxModelHandler  env com.a2ia.data.OnnxModelHandler  equals com.a2ia.data.OnnxModelHandler  forEachIndexed com.a2ia.data.OnnxModelHandler  getFOREachIndexed com.a2ia.data.OnnxModelHandler  getForEachIndexed com.a2ia.data.OnnxModelHandler  getLET com.a2ia.data.OnnxModelHandler  getLONGArrayOf com.a2ia.data.OnnxModelHandler  getLet com.a2ia.data.OnnxModelHandler  getLongArrayOf com.a2ia.data.OnnxModelHandler  getMAPOf com.a2ia.data.OnnxModelHandler  getMapOf com.a2ia.data.OnnxModelHandler  getTAKE com.a2ia.data.OnnxModelHandler  getTO com.a2ia.data.OnnxModelHandler  getTake com.a2ia.data.OnnxModelHandler  getTo com.a2ia.data.OnnxModelHandler  getUNTIL com.a2ia.data.OnnxModelHandler  getUSE com.a2ia.data.OnnxModelHandler  getUntil com.a2ia.data.OnnxModelHandler  getUse com.a2ia.data.OnnxModelHandler  let com.a2ia.data.OnnxModelHandler  
loadOnnxModel com.a2ia.data.OnnxModelHandler  longArrayOf com.a2ia.data.OnnxModelHandler  mapOf com.a2ia.data.OnnxModelHandler  model com.a2ia.data.OnnxModelHandler  
modelFilePath com.a2ia.data.OnnxModelHandler  preProcessImage com.a2ia.data.OnnxModelHandler  predict com.a2ia.data.OnnxModelHandler  take com.a2ia.data.OnnxModelHandler  to com.a2ia.data.OnnxModelHandler  until com.a2ia.data.OnnxModelHandler  use com.a2ia.data.OnnxModelHandler  Bitmap (com.a2ia.data.OnnxModelHandler.Companion  	ByteArray (com.a2ia.data.OnnxModelHandler.Companion  Context (com.a2ia.data.OnnxModelHandler.Companion  DEFAULT_MODEL_PATH (com.a2ia.data.OnnxModelHandler.Companion  	Exception (com.a2ia.data.OnnxModelHandler.Companion  
FloatArray (com.a2ia.data.OnnxModelHandler.Companion  FloatBuffer (com.a2ia.data.OnnxModelHandler.Companion  
INPUT_SIZE (com.a2ia.data.OnnxModelHandler.Companion  Imgproc (com.a2ia.data.OnnxModelHandler.Companion  Int (com.a2ia.data.OnnxModelHandler.Companion  Log (com.a2ia.data.OnnxModelHandler.Companion  	LongArray (com.a2ia.data.OnnxModelHandler.Companion  Mat (com.a2ia.data.OnnxModelHandler.Companion  
OnnxTensor (com.a2ia.data.OnnxModelHandler.Companion  OrtEnvironment (com.a2ia.data.OnnxModelHandler.Companion  OrtException (com.a2ia.data.OnnxModelHandler.Companion  
OrtSession (com.a2ia.data.OnnxModelHandler.Companion  RuntimeException (com.a2ia.data.OnnxModelHandler.Companion  Size (com.a2ia.data.OnnxModelHandler.Companion  String (com.a2ia.data.OnnxModelHandler.Companion  TAG (com.a2ia.data.OnnxModelHandler.Companion  Utils (com.a2ia.data.OnnxModelHandler.Companion  forEachIndexed (com.a2ia.data.OnnxModelHandler.Companion  getFOREachIndexed (com.a2ia.data.OnnxModelHandler.Companion  getForEachIndexed (com.a2ia.data.OnnxModelHandler.Companion  getLET (com.a2ia.data.OnnxModelHandler.Companion  getLONGArrayOf (com.a2ia.data.OnnxModelHandler.Companion  getLet (com.a2ia.data.OnnxModelHandler.Companion  getLongArrayOf (com.a2ia.data.OnnxModelHandler.Companion  getMAPOf (com.a2ia.data.OnnxModelHandler.Companion  getMapOf (com.a2ia.data.OnnxModelHandler.Companion  getTAKE (com.a2ia.data.OnnxModelHandler.Companion  getTO (com.a2ia.data.OnnxModelHandler.Companion  getTake (com.a2ia.data.OnnxModelHandler.Companion  getTo (com.a2ia.data.OnnxModelHandler.Companion  getUNTIL (com.a2ia.data.OnnxModelHandler.Companion  getUSE (com.a2ia.data.OnnxModelHandler.Companion  getUntil (com.a2ia.data.OnnxModelHandler.Companion  getUse (com.a2ia.data.OnnxModelHandler.Companion  invoke (com.a2ia.data.OnnxModelHandler.Companion  let (com.a2ia.data.OnnxModelHandler.Companion  longArrayOf (com.a2ia.data.OnnxModelHandler.Companion  mapOf (com.a2ia.data.OnnxModelHandler.Companion  take (com.a2ia.data.OnnxModelHandler.Companion  to (com.a2ia.data.OnnxModelHandler.Companion  until (com.a2ia.data.OnnxModelHandler.Companion  use (com.a2ia.data.OnnxModelHandler.Companion  Input com.a2ia.data.OutputFormat  JPEG com.a2ia.data.OutputFormat  PNG com.a2ia.data.OutputFormat  Bitmap com.a2ia.data.ProcessedImage  	ByteArray com.a2ia.data.ProcessedImage  ByteArrayOutputStream com.a2ia.data.ProcessedImage  ImageFormat com.a2ia.data.ProcessedImage  ProcessedImage com.a2ia.data.ProcessedImage  apply com.a2ia.data.ProcessedImage  buffer com.a2ia.data.ProcessedImage  getAPPLY com.a2ia.data.ProcessedImage  getApply com.a2ia.data.ProcessedImage  invoke com.a2ia.data.ProcessedImage  Bitmap &com.a2ia.data.ProcessedImage.Companion  	ByteArray &com.a2ia.data.ProcessedImage.Companion  ByteArrayOutputStream &com.a2ia.data.ProcessedImage.Companion  ImageFormat &com.a2ia.data.ProcessedImage.Companion  ProcessedImage &com.a2ia.data.ProcessedImage.Companion  apply &com.a2ia.data.ProcessedImage.Companion  getAPPLY &com.a2ia.data.ProcessedImage.Companion  getApply &com.a2ia.data.ProcessedImage.Companion  invoke &com.a2ia.data.ProcessedImage.Companion  Log com.a2ia.data.ProcessorLogs  String com.a2ia.data.ProcessorLogs  Bitmap com.a2ia.data.TFLiteHandler  
ByteBuffer com.a2ia.data.TFLiteHandler  	ByteOrder com.a2ia.data.TFLiteHandler  ChequeProcessorSDK com.a2ia.data.TFLiteHandler  Context com.a2ia.data.TFLiteHandler  DataType com.a2ia.data.TFLiteHandler  	Exception com.a2ia.data.TFLiteHandler  Float com.a2ia.data.TFLiteHandler  
FloatArray com.a2ia.data.TFLiteHandler  IOException com.a2ia.data.TFLiteHandler  Imgproc com.a2ia.data.TFLiteHandler  IncompatibleClassChangeError com.a2ia.data.TFLiteHandler  Int com.a2ia.data.TFLiteHandler  IntArray com.a2ia.data.TFLiteHandler  Log com.a2ia.data.TFLiteHandler  Mat com.a2ia.data.TFLiteHandler  ModelV2 com.a2ia.data.TFLiteHandler  OutOfMemoryError com.a2ia.data.TFLiteHandler  Pair com.a2ia.data.TFLiteHandler  Random com.a2ia.data.TFLiteHandler  String com.a2ia.data.TFLiteHandler  
StringBuilder com.a2ia.data.TFLiteHandler  System com.a2ia.data.TFLiteHandler  TAG com.a2ia.data.TFLiteHandler  TensorBuffer com.a2ia.data.TFLiteHandler  Utils com.a2ia.data.TFLiteHandler  arrayOf com.a2ia.data.TFLiteHandler  class_names com.a2ia.data.TFLiteHandler  close com.a2ia.data.TFLiteHandler  context com.a2ia.data.TFLiteHandler  convertMatToBitmap com.a2ia.data.TFLiteHandler  	divAssign com.a2ia.data.TFLiteHandler  	emptyList com.a2ia.data.TFLiteHandler  equals com.a2ia.data.TFLiteHandler  execute com.a2ia.data.TFLiteHandler  format com.a2ia.data.TFLiteHandler  getClass_names com.a2ia.data.TFLiteHandler  getDIVAssign com.a2ia.data.TFLiteHandler  getDivAssign com.a2ia.data.TFLiteHandler  getEMPTYList com.a2ia.data.TFLiteHandler  getEmptyList com.a2ia.data.TFLiteHandler  	getFORMAT com.a2ia.data.TFLiteHandler  	getFormat com.a2ia.data.TFLiteHandler  
getINTArrayOf com.a2ia.data.TFLiteHandler  
getISNotEmpty com.a2ia.data.TFLiteHandler  
getIntArrayOf com.a2ia.data.TFLiteHandler  
getIsNotEmpty com.a2ia.data.TFLiteHandler  	getKOTLIN com.a2ia.data.TFLiteHandler  	getKotlin com.a2ia.data.TFLiteHandler  getMUTABLEListOf com.a2ia.data.TFLiteHandler  getMutableListOf com.a2ia.data.TFLiteHandler  
getPLUSAssign com.a2ia.data.TFLiteHandler  
getPlusAssign com.a2ia.data.TFLiteHandler  getUNTIL com.a2ia.data.TFLiteHandler  getUntil com.a2ia.data.TFLiteHandler  indices com.a2ia.data.TFLiteHandler  
intArrayOf com.a2ia.data.TFLiteHandler  
isNotEmpty com.a2ia.data.TFLiteHandler  kotlin com.a2ia.data.TFLiteHandler  makeDefaultResult com.a2ia.data.TFLiteHandler  
mutableListOf com.a2ia.data.TFLiteHandler  
plusAssign com.a2ia.data.TFLiteHandler  until com.a2ia.data.TFLiteHandler  Bitmap %com.a2ia.data.TFLiteHandler.Companion  
ByteBuffer %com.a2ia.data.TFLiteHandler.Companion  	ByteOrder %com.a2ia.data.TFLiteHandler.Companion  ChequeProcessorSDK %com.a2ia.data.TFLiteHandler.Companion  Context %com.a2ia.data.TFLiteHandler.Companion  DataType %com.a2ia.data.TFLiteHandler.Companion  	Exception %com.a2ia.data.TFLiteHandler.Companion  Float %com.a2ia.data.TFLiteHandler.Companion  
FloatArray %com.a2ia.data.TFLiteHandler.Companion  IOException %com.a2ia.data.TFLiteHandler.Companion  Imgproc %com.a2ia.data.TFLiteHandler.Companion  IncompatibleClassChangeError %com.a2ia.data.TFLiteHandler.Companion  Int %com.a2ia.data.TFLiteHandler.Companion  IntArray %com.a2ia.data.TFLiteHandler.Companion  Log %com.a2ia.data.TFLiteHandler.Companion  Mat %com.a2ia.data.TFLiteHandler.Companion  ModelV2 %com.a2ia.data.TFLiteHandler.Companion  OutOfMemoryError %com.a2ia.data.TFLiteHandler.Companion  Pair %com.a2ia.data.TFLiteHandler.Companion  Random %com.a2ia.data.TFLiteHandler.Companion  String %com.a2ia.data.TFLiteHandler.Companion  
StringBuilder %com.a2ia.data.TFLiteHandler.Companion  System %com.a2ia.data.TFLiteHandler.Companion  TAG %com.a2ia.data.TFLiteHandler.Companion  TensorBuffer %com.a2ia.data.TFLiteHandler.Companion  Utils %com.a2ia.data.TFLiteHandler.Companion  arrayOf %com.a2ia.data.TFLiteHandler.Companion  class_names %com.a2ia.data.TFLiteHandler.Companion  	divAssign %com.a2ia.data.TFLiteHandler.Companion  	emptyList %com.a2ia.data.TFLiteHandler.Companion  format %com.a2ia.data.TFLiteHandler.Companion  
getARRAYOf %com.a2ia.data.TFLiteHandler.Companion  
getArrayOf %com.a2ia.data.TFLiteHandler.Companion  getDIVAssign %com.a2ia.data.TFLiteHandler.Companion  getDivAssign %com.a2ia.data.TFLiteHandler.Companion  getEMPTYList %com.a2ia.data.TFLiteHandler.Companion  getEmptyList %com.a2ia.data.TFLiteHandler.Companion  	getFORMAT %com.a2ia.data.TFLiteHandler.Companion  	getFormat %com.a2ia.data.TFLiteHandler.Companion  
getINTArrayOf %com.a2ia.data.TFLiteHandler.Companion  
getISNotEmpty %com.a2ia.data.TFLiteHandler.Companion  
getIntArrayOf %com.a2ia.data.TFLiteHandler.Companion  
getIsNotEmpty %com.a2ia.data.TFLiteHandler.Companion  	getKOTLIN %com.a2ia.data.TFLiteHandler.Companion  	getKotlin %com.a2ia.data.TFLiteHandler.Companion  getMUTABLEListOf %com.a2ia.data.TFLiteHandler.Companion  getMutableListOf %com.a2ia.data.TFLiteHandler.Companion  
getPLUSAssign %com.a2ia.data.TFLiteHandler.Companion  
getPlusAssign %com.a2ia.data.TFLiteHandler.Companion  getUNTIL %com.a2ia.data.TFLiteHandler.Companion  getUntil %com.a2ia.data.TFLiteHandler.Companion  indices %com.a2ia.data.TFLiteHandler.Companion  
intArrayOf %com.a2ia.data.TFLiteHandler.Companion  invoke %com.a2ia.data.TFLiteHandler.Companion  
isNotEmpty %com.a2ia.data.TFLiteHandler.Companion  kotlin %com.a2ia.data.TFLiteHandler.Companion  
mutableListOf %com.a2ia.data.TFLiteHandler.Companion  
plusAssign %com.a2ia.data.TFLiteHandler.Companion  until %com.a2ia.data.TFLiteHandler.Companion  A2iABoolean com.a2ia.data.VerboseDetails  ImageOutputFormat com.a2ia.data.VerboseDetails  locatedDocumentImage com.a2ia.data.VerboseDetails  locatedDocumentImageFormat com.a2ia.data.VerboseDetails  preprocessedImage com.a2ia.data.VerboseDetails  preprocessedImageFormat com.a2ia.data.VerboseDetails  Any com.a2ia.data.YOLOModelHandler  Bitmap com.a2ia.data.YOLOModelHandler  
ByteBuffer com.a2ia.data.YOLOModelHandler  	ByteOrder com.a2ia.data.YOLOModelHandler  Canvas com.a2ia.data.YOLOModelHandler  Color com.a2ia.data.YOLOModelHandler  Context com.a2ia.data.YOLOModelHandler  	Exception com.a2ia.data.YOLOModelHandler  FileChannel com.a2ia.data.YOLOModelHandler  FileInputStream com.a2ia.data.YOLOModelHandler  Float com.a2ia.data.YOLOModelHandler  
FloatArray com.a2ia.data.YOLOModelHandler  Int com.a2ia.data.YOLOModelHandler  IntArray com.a2ia.data.YOLOModelHandler  Interpreter com.a2ia.data.YOLOModelHandler  List com.a2ia.data.YOLOModelHandler  Log com.a2ia.data.YOLOModelHandler  Rect com.a2ia.data.YOLOModelHandler  String com.a2ia.data.YOLOModelHandler  TAG com.a2ia.data.YOLOModelHandler  Triple com.a2ia.data.YOLOModelHandler  Unit com.a2ia.data.YOLOModelHandler  arrayOf com.a2ia.data.YOLOModelHandler  classLimits com.a2ia.data.YOLOModelHandler  
classNames com.a2ia.data.YOLOModelHandler  coerceIn com.a2ia.data.YOLOModelHandler  
component1 com.a2ia.data.YOLOModelHandler  
component2 com.a2ia.data.YOLOModelHandler  context com.a2ia.data.YOLOModelHandler  
detectRegions com.a2ia.data.YOLOModelHandler  	emptyList com.a2ia.data.YOLOModelHandler  filter com.a2ia.data.YOLOModelHandler  filterDetections com.a2ia.data.YOLOModelHandler  
getARRAYOf com.a2ia.data.YOLOModelHandler  
getArrayOf com.a2ia.data.YOLOModelHandler  getCOERCEIn com.a2ia.data.YOLOModelHandler  getCoerceIn com.a2ia.data.YOLOModelHandler  
getComponent1 com.a2ia.data.YOLOModelHandler  
getComponent2 com.a2ia.data.YOLOModelHandler  getEMPTYList com.a2ia.data.YOLOModelHandler  getEmptyList com.a2ia.data.YOLOModelHandler  	getFILTER com.a2ia.data.YOLOModelHandler  	getFilter com.a2ia.data.YOLOModelHandler  getMAPOf com.a2ia.data.YOLOModelHandler  getMAXOf com.a2ia.data.YOLOModelHandler  getMAXOrNull com.a2ia.data.YOLOModelHandler  getMINOrNull com.a2ia.data.YOLOModelHandler  getMUTABLEListOf com.a2ia.data.YOLOModelHandler  getMUTABLEMapOf com.a2ia.data.YOLOModelHandler  getMapOf com.a2ia.data.YOLOModelHandler  getMaxOf com.a2ia.data.YOLOModelHandler  getMaxOrNull com.a2ia.data.YOLOModelHandler  getMinOrNull com.a2ia.data.YOLOModelHandler  getMutableListOf com.a2ia.data.YOLOModelHandler  getMutableMapOf com.a2ia.data.YOLOModelHandler  getSET com.a2ia.data.YOLOModelHandler  getSORTEDByDescending com.a2ia.data.YOLOModelHandler  getSTACKTraceToString com.a2ia.data.YOLOModelHandler  getSet com.a2ia.data.YOLOModelHandler  getSortedByDescending com.a2ia.data.YOLOModelHandler  getStackTraceToString com.a2ia.data.YOLOModelHandler  getTAKE com.a2ia.data.YOLOModelHandler  getTO com.a2ia.data.YOLOModelHandler  
getTRIMIndent com.a2ia.data.YOLOModelHandler  getTake com.a2ia.data.YOLOModelHandler  getTo com.a2ia.data.YOLOModelHandler  
getTrimIndent com.a2ia.data.YOLOModelHandler  getUNTIL com.a2ia.data.YOLOModelHandler  getUntil com.a2ia.data.YOLOModelHandler  	inputSize com.a2ia.data.YOLOModelHandler  interpreter com.a2ia.data.YOLOModelHandler  invoke com.a2ia.data.YOLOModelHandler  	javaClass com.a2ia.data.YOLOModelHandler  log com.a2ia.data.YOLOModelHandler  logCallback com.a2ia.data.YOLOModelHandler  mapOf com.a2ia.data.YOLOModelHandler  maxOf com.a2ia.data.YOLOModelHandler  	maxOrNull com.a2ia.data.YOLOModelHandler  	minOrNull com.a2ia.data.YOLOModelHandler  
mutableListOf com.a2ia.data.YOLOModelHandler  mutableMapOf com.a2ia.data.YOLOModelHandler  set com.a2ia.data.YOLOModelHandler  sortedByDescending com.a2ia.data.YOLOModelHandler  stackTraceToString com.a2ia.data.YOLOModelHandler  take com.a2ia.data.YOLOModelHandler  to com.a2ia.data.YOLOModelHandler  
trimIndent com.a2ia.data.YOLOModelHandler  until com.a2ia.data.YOLOModelHandler  Any (com.a2ia.data.YOLOModelHandler.Companion  Bitmap (com.a2ia.data.YOLOModelHandler.Companion  
ByteBuffer (com.a2ia.data.YOLOModelHandler.Companion  	ByteOrder (com.a2ia.data.YOLOModelHandler.Companion  Canvas (com.a2ia.data.YOLOModelHandler.Companion  Color (com.a2ia.data.YOLOModelHandler.Companion  Context (com.a2ia.data.YOLOModelHandler.Companion  	Exception (com.a2ia.data.YOLOModelHandler.Companion  FileChannel (com.a2ia.data.YOLOModelHandler.Companion  FileInputStream (com.a2ia.data.YOLOModelHandler.Companion  Float (com.a2ia.data.YOLOModelHandler.Companion  
FloatArray (com.a2ia.data.YOLOModelHandler.Companion  Int (com.a2ia.data.YOLOModelHandler.Companion  IntArray (com.a2ia.data.YOLOModelHandler.Companion  Interpreter (com.a2ia.data.YOLOModelHandler.Companion  List (com.a2ia.data.YOLOModelHandler.Companion  Log (com.a2ia.data.YOLOModelHandler.Companion  Rect (com.a2ia.data.YOLOModelHandler.Companion  String (com.a2ia.data.YOLOModelHandler.Companion  TAG (com.a2ia.data.YOLOModelHandler.Companion  Triple (com.a2ia.data.YOLOModelHandler.Companion  Unit (com.a2ia.data.YOLOModelHandler.Companion  arrayOf (com.a2ia.data.YOLOModelHandler.Companion  coerceIn (com.a2ia.data.YOLOModelHandler.Companion  
component1 (com.a2ia.data.YOLOModelHandler.Companion  
component2 (com.a2ia.data.YOLOModelHandler.Companion  	emptyList (com.a2ia.data.YOLOModelHandler.Companion  filter (com.a2ia.data.YOLOModelHandler.Companion  
getARRAYOf (com.a2ia.data.YOLOModelHandler.Companion  
getArrayOf (com.a2ia.data.YOLOModelHandler.Companion  getCOERCEIn (com.a2ia.data.YOLOModelHandler.Companion  getCoerceIn (com.a2ia.data.YOLOModelHandler.Companion  
getComponent1 (com.a2ia.data.YOLOModelHandler.Companion  
getComponent2 (com.a2ia.data.YOLOModelHandler.Companion  getEMPTYList (com.a2ia.data.YOLOModelHandler.Companion  getEmptyList (com.a2ia.data.YOLOModelHandler.Companion  	getFILTER (com.a2ia.data.YOLOModelHandler.Companion  	getFilter (com.a2ia.data.YOLOModelHandler.Companion  getMAPOf (com.a2ia.data.YOLOModelHandler.Companion  getMAXOf (com.a2ia.data.YOLOModelHandler.Companion  getMAXOrNull (com.a2ia.data.YOLOModelHandler.Companion  getMINOrNull (com.a2ia.data.YOLOModelHandler.Companion  getMUTABLEListOf (com.a2ia.data.YOLOModelHandler.Companion  getMUTABLEMapOf (com.a2ia.data.YOLOModelHandler.Companion  getMapOf (com.a2ia.data.YOLOModelHandler.Companion  getMaxOf (com.a2ia.data.YOLOModelHandler.Companion  getMaxOrNull (com.a2ia.data.YOLOModelHandler.Companion  getMinOrNull (com.a2ia.data.YOLOModelHandler.Companion  getMutableListOf (com.a2ia.data.YOLOModelHandler.Companion  getMutableMapOf (com.a2ia.data.YOLOModelHandler.Companion  getSET (com.a2ia.data.YOLOModelHandler.Companion  getSORTEDByDescending (com.a2ia.data.YOLOModelHandler.Companion  getSTACKTraceToString (com.a2ia.data.YOLOModelHandler.Companion  getSet (com.a2ia.data.YOLOModelHandler.Companion  getSortedByDescending (com.a2ia.data.YOLOModelHandler.Companion  getStackTraceToString (com.a2ia.data.YOLOModelHandler.Companion  getTAKE (com.a2ia.data.YOLOModelHandler.Companion  getTO (com.a2ia.data.YOLOModelHandler.Companion  
getTRIMIndent (com.a2ia.data.YOLOModelHandler.Companion  getTake (com.a2ia.data.YOLOModelHandler.Companion  getTo (com.a2ia.data.YOLOModelHandler.Companion  
getTrimIndent (com.a2ia.data.YOLOModelHandler.Companion  getUNTIL (com.a2ia.data.YOLOModelHandler.Companion  getUntil (com.a2ia.data.YOLOModelHandler.Companion  invoke (com.a2ia.data.YOLOModelHandler.Companion  	javaClass (com.a2ia.data.YOLOModelHandler.Companion  mapOf (com.a2ia.data.YOLOModelHandler.Companion  maxOf (com.a2ia.data.YOLOModelHandler.Companion  	maxOrNull (com.a2ia.data.YOLOModelHandler.Companion  	minOrNull (com.a2ia.data.YOLOModelHandler.Companion  
mutableListOf (com.a2ia.data.YOLOModelHandler.Companion  mutableMapOf (com.a2ia.data.YOLOModelHandler.Companion  set (com.a2ia.data.YOLOModelHandler.Companion  sortedByDescending (com.a2ia.data.YOLOModelHandler.Companion  stackTraceToString (com.a2ia.data.YOLOModelHandler.Companion  take (com.a2ia.data.YOLOModelHandler.Companion  to (com.a2ia.data.YOLOModelHandler.Companion  
trimIndent (com.a2ia.data.YOLOModelHandler.Companion  until (com.a2ia.data.YOLOModelHandler.Companion  ModelV2 com.a2ia.data.ml  Outputs com.a2ia.data.ml.ModelV2  close com.a2ia.data.ml.ModelV2  newInstance com.a2ia.data.ml.ModelV2  process com.a2ia.data.ml.ModelV2  getOUTPUTFeature0AsTensorBuffer  com.a2ia.data.ml.ModelV2.Outputs  getOutputFeature0AsTensorBuffer  com.a2ia.data.ml.ModelV2.Outputs  outputFeature0AsTensorBuffer  com.a2ia.data.ml.ModelV2.Outputs  setOutputFeature0AsTensorBuffer  com.a2ia.data.ml.ModelV2.Outputs  Task com.google.android.gms.tasks  Tasks com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  await "com.google.android.gms.tasks.Tasks  
InputImage com.google.mlkit.vision.common  
fromBitmap )com.google.mlkit.vision.common.InputImage  SubjectSegmentation ,com.google.mlkit.vision.segmentation.subject  SubjectSegmentationResult ,com.google.mlkit.vision.segmentation.subject  SubjectSegmenter ,com.google.mlkit.vision.segmentation.subject  SubjectSegmenterOptions ,com.google.mlkit.vision.segmentation.subject  	getClient @com.google.mlkit.vision.segmentation.subject.SubjectSegmentation  foregroundBitmap Fcom.google.mlkit.vision.segmentation.subject.SubjectSegmentationResult  getFOREGROUNDBitmap Fcom.google.mlkit.vision.segmentation.subject.SubjectSegmentationResult  getForegroundBitmap Fcom.google.mlkit.vision.segmentation.subject.SubjectSegmentationResult  setForegroundBitmap Fcom.google.mlkit.vision.segmentation.subject.SubjectSegmentationResult  process =com.google.mlkit.vision.segmentation.subject.SubjectSegmenter  Builder Dcom.google.mlkit.vision.segmentation.subject.SubjectSegmenterOptions  build Lcom.google.mlkit.vision.segmentation.subject.SubjectSegmenterOptions.Builder  enableForegroundBitmap Lcom.google.mlkit.vision.segmentation.subject.SubjectSegmenterOptions.Builder  Text com.google.mlkit.vision.text  TextRecognition com.google.mlkit.vision.text  TextRecognizer com.google.mlkit.vision.text  Element !com.google.mlkit.vision.text.Text  Line !com.google.mlkit.vision.text.Text  	TextBlock !com.google.mlkit.vision.text.Text  getTEXT !com.google.mlkit.vision.text.Text  
getTEXTBlocks !com.google.mlkit.vision.text.Text  getText !com.google.mlkit.vision.text.Text  
getTextBlocks !com.google.mlkit.vision.text.Text  setText !com.google.mlkit.vision.text.Text  
setTextBlocks !com.google.mlkit.vision.text.Text  text !com.google.mlkit.vision.text.Text  
textBlocks !com.google.mlkit.vision.text.Text  
confidence )com.google.mlkit.vision.text.Text.Element  
getCONFIDENCE )com.google.mlkit.vision.text.Text.Element  
getConfidence )com.google.mlkit.vision.text.Text.Element  
setConfidence )com.google.mlkit.vision.text.Text.Element  elements &com.google.mlkit.vision.text.Text.Line  getELEMENTS &com.google.mlkit.vision.text.Text.Line  getElements &com.google.mlkit.vision.text.Text.Line  setElements &com.google.mlkit.vision.text.Text.Line  getLines *com.google.mlkit.vision.text.Text.TextBase  getLINES +com.google.mlkit.vision.text.Text.TextBlock  getLines +com.google.mlkit.vision.text.Text.TextBlock  lines +com.google.mlkit.vision.text.Text.TextBlock  setLines +com.google.mlkit.vision.text.Text.TextBlock  	getClient ,com.google.mlkit.vision.text.TextRecognition  process +com.google.mlkit.vision.text.TextRecognizer  TextRecognizerOptions "com.google.mlkit.vision.text.latin  DEFAULT_OPTIONS 8com.google.mlkit.vision.text.latin.TextRecognizerOptions  Array java.io  	ArrayList java.io  AtomicReference java.io  Bitmap java.io  ByteArrayOutputStream java.io  Canvas java.io  ChequeProcessorSDK java.io  ChequeUtils java.io  ClockwiseComparator java.io  Collections java.io  Color java.io  
Comparator java.io  Core java.io  CountDownLatch java.io  CvType java.io  Date java.io  	Executors java.io  File java.io  FileDescriptor java.io  FileInputStream java.io  FileOutputStream java.io  HashMap java.io  IOException java.io  IllegalStateException java.io  Imgproc java.io  
InputImage java.io  InputStream java.io  Int java.io  IntArray java.io  JvmField java.io  	JvmStatic java.io  Locale java.io  Log java.io  Mat java.io  MatOfInt java.io  
MatOfPoint java.io  MatOfPoint2f java.io  Math java.io  Matrix java.io  OnnxModelHandler java.io  OpenCVLoader java.io  
OpenCVRect java.io  Paint java.io  Pair java.io  Point java.io  
ProcessorLogs java.io  Rect java.io  SEGMENTATION_TIMEOUT_SECONDS java.io  Scalar java.io  SimpleDateFormat java.io  Size java.io  String java.io  SubjectSegmentation java.io  SubjectSegmenterOptions java.io  System java.io  TAG java.io  
TFLiteHandler java.io  Tasks java.io  TextRecognition java.io  TextRecognizerOptions java.io  TimeUnit java.io  Utils java.io  YOLOModelHandler java.io  apply java.io  arrayOf java.io  average java.io  coerceIn java.io  	compareBy java.io  
doubleArrayOf java.io  filter java.io  forEach java.io  forEachIndexed java.io  format java.io  indices java.io  instance java.io  
intArrayOf java.io  isDigit java.io  
isNotEmpty java.io  java java.io  last java.io  let java.io  listOf java.io  map java.io  max java.io  maxOf java.io  minOf java.io  
plusAssign java.io  println java.io  sortedBy java.io  
sortedWith java.io  	substring java.io  take java.io  thenBy java.io  toTypedArray java.io  until java.io  use java.io  	withIndex java.io  reset java.io.ByteArrayOutputStream  toByteArray java.io.ByteArrayOutputStream  absolutePath java.io.File  exists java.io.File  getABSOLUTEPath java.io.File  getAbsolutePath java.io.File  length java.io.File  mkdirs java.io.File  setAbsolutePath java.io.File  channel java.io.FileInputStream  
getCHANNEL java.io.FileInputStream  
getChannel java.io.FileInputStream  
setChannel java.io.FileInputStream  getUSE java.io.FileOutputStream  getUse java.io.FileOutputStream  use java.io.FileOutputStream  message java.io.IOException  	available java.io.InputStream  close java.io.InputStream  getUSE java.io.InputStream  getUse java.io.InputStream  read java.io.InputStream  use java.io.InputStream  reset java.io.OutputStream  toByteArray java.io.OutputStream  use java.io.OutputStream  A2iABoolean 	java.lang  AddressLineItem 	java.lang  Array 	java.lang  	ArrayList 	java.lang  AtomicReference 	java.lang  Bitmap 	java.lang  
BitmapFactory 	java.lang  	ByteArray 	java.lang  ByteArrayOutputStream 	java.lang  
ByteBuffer 	java.lang  	ByteOrder 	java.lang  Canvas 	java.lang  CheckDocumentResults 	java.lang  
CheckInput 	java.lang  ChequeProcessorSDK 	java.lang  ChequeUtils 	java.lang  ClockwiseComparator 	java.lang  Collections 	java.lang  Color 	java.lang  
Comparator 	java.lang  Core 	java.lang  CountDownLatch 	java.lang  CvType 	java.lang  DEFAULT_MODEL_PATH 	java.lang  DataType 	java.lang  Date 	java.lang  	Exception 	java.lang  	Executors 	java.lang  File 	java.lang  FileChannel 	java.lang  FileInputStream 	java.lang  FileOutputStream 	java.lang  
FloatArray 	java.lang  FloatBuffer 	java.lang  HashMap 	java.lang  
INPUT_SIZE 	java.lang  IllegalStateException 	java.lang  ImageFormat 	java.lang  ImageOutputFormat 	java.lang  Imgproc 	java.lang  IncompatibleClassChangeError 	java.lang  
InputImage 	java.lang  Int 	java.lang  IntArray 	java.lang  Interpreter 	java.lang  KEY_CVN_CODE 	java.lang  KEY_CVN_RECT 	java.lang  
KEY_CVN_SCORE 	java.lang  KEY_ERROR_MESSAGE 	java.lang  KEY_HAS_SIGNATURE 	java.lang  KEY_HAS_VALID_CHEQUE_CONTOUR 	java.lang  KEY_IS_GENUINE 	java.lang  KEY_IS_SUCCESSFUL 	java.lang  
KEY_MICR_CODE 	java.lang  
KEY_MICR_RECT 	java.lang  KEY_MICR_SCORE 	java.lang  KEY_ORIGINAL_IMAGE 	java.lang  KEY_PROCESSED_IMAGE 	java.lang  KEY_SIGNATURE_RECT 	java.lang  KEY_SIGNATURE_SCORE 	java.lang  KEY_VALID_MICR_POSITION 	java.lang  Locale 	java.lang  Log 	java.lang  Mat 	java.lang  MatOfInt 	java.lang  
MatOfPoint 	java.lang  MatOfPoint2f 	java.lang  Math 	java.lang  Matrix 	java.lang  MemoryImage 	java.lang  ModelV2 	java.lang  OnnxModelHandler 	java.lang  
OnnxTensor 	java.lang  OpenCVLoader 	java.lang  
OpenCVRect 	java.lang  OrtEnvironment 	java.lang  OutOfMemoryError 	java.lang  OutputFormat 	java.lang  Paint 	java.lang  Pair 	java.lang  Point 	java.lang  ProcessedImage 	java.lang  
ProcessorLogs 	java.lang  Random 	java.lang  Rect 	java.lang  Result 	java.lang  RuntimeException 	java.lang  SEGMENTATION_TIMEOUT_SECONDS 	java.lang  Scalar 	java.lang  SimpleDateFormat 	java.lang  Size 	java.lang  String 	java.lang  
StringBuilder 	java.lang  SubjectSegmentation 	java.lang  SubjectSegmenterOptions 	java.lang  System 	java.lang  TAG 	java.lang  
TFLiteHandler 	java.lang  Tasks 	java.lang  TensorBuffer 	java.lang  TextRecognition 	java.lang  TextRecognizerOptions 	java.lang  TimeUnit 	java.lang  Triple 	java.lang  Utils 	java.lang  VerboseDetails 	java.lang  YOLOModelHandler 	java.lang  also 	java.lang  android 	java.lang  apply 	java.lang  arrayOf 	java.lang  atan2 	java.lang  average 	java.lang  class_names 	java.lang  coerceIn 	java.lang  	compareBy 	java.lang  
component1 	java.lang  
component2 	java.lang  	divAssign 	java.lang  
doubleArrayOf 	java.lang  	emptyList 	java.lang  filter 	java.lang  floatArrayOf 	java.lang  forEach 	java.lang  forEachIndexed 	java.lang  format 	java.lang  	getOrNull 	java.lang  indices 	java.lang  instance 	java.lang  
intArrayOf 	java.lang  isDigit 	java.lang  
isNotEmpty 	java.lang  java 	java.lang  	javaClass 	java.lang  kotlin 	java.lang  last 	java.lang  let 	java.lang  listOf 	java.lang  longArrayOf 	java.lang  map 	java.lang  mapOf 	java.lang  max 	java.lang  maxOf 	java.lang  	maxOrNull 	java.lang  minOf 	java.lang  	minOrNull 	java.lang  
mutableListOf 	java.lang  mutableMapOf 	java.lang  
plusAssign 	java.lang  pow 	java.lang  println 	java.lang  result 	java.lang  run 	java.lang  set 	java.lang  sortedBy 	java.lang  sortedByDescending 	java.lang  
sortedWith 	java.lang  sqrt 	java.lang  stackTraceToString 	java.lang  	substring 	java.lang  take 	java.lang  thenBy 	java.lang  to 	java.lang  toTypedArray 	java.lang  
trimIndent 	java.lang  until 	java.lang  use 	java.lang  	withIndex 	java.lang  
getSIMPLEName java.lang.Class  
getSimpleName java.lang.Class  
setSimpleName java.lang.Class  
simpleName java.lang.Class  getJAVAClass java.lang.Exception  getJavaClass java.lang.Exception  getSTACKTraceToString java.lang.Exception  getStackTraceToString java.lang.Exception  	javaClass java.lang.Exception  message java.lang.Exception  printStackTrace java.lang.Exception  stackTraceToString java.lang.Exception  message &java.lang.IncompatibleClassChangeError  round java.lang.Math  message java.lang.OutOfMemoryError  <SAM-CONSTRUCTOR> java.lang.Runnable  append java.lang.StringBuilder  toString java.lang.StringBuilder  gc java.lang.System  Buffer java.nio  
ByteBuffer java.nio  	ByteOrder java.nio  FloatBuffer java.nio  MappedByteBuffer java.nio  
asFloatBuffer java.nio.Buffer  capacity java.nio.Buffer  get java.nio.Buffer  order java.nio.Buffer  putFloat java.nio.Buffer  rewind java.nio.Buffer  allocateDirect java.nio.ByteBuffer  
asFloatBuffer java.nio.ByteBuffer  order java.nio.ByteBuffer  putFloat java.nio.ByteBuffer  rewind java.nio.ByteBuffer  nativeOrder java.nio.ByteOrder  capacity java.nio.FloatBuffer  get java.nio.FloatBuffer  wrap java.nio.FloatBuffer  FileChannel java.nio.channels  MapMode java.nio.channels.FileChannel  map java.nio.channels.FileChannel  	READ_ONLY %java.nio.channels.FileChannel.MapMode  map 2java.nio.channels.spi.AbstractInterruptibleChannel  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Array 	java.util  	ArrayList 	java.util  Arrays 	java.util  AtomicReference 	java.util  Bitmap 	java.util  Canvas 	java.util  ChequeProcessorSDK 	java.util  ChequeUtils 	java.util  ClockwiseComparator 	java.util  Collections 	java.util  Color 	java.util  
Comparator 	java.util  Core 	java.util  CountDownLatch 	java.util  CvType 	java.util  Date 	java.util  	Executors 	java.util  File 	java.util  FileOutputStream 	java.util  HashMap 	java.util  IllegalStateException 	java.util  Imgproc 	java.util  
InputImage 	java.util  Int 	java.util  IntArray 	java.util  JvmField 	java.util  	JvmStatic 	java.util  List 	java.util  Locale 	java.util  Log 	java.util  Mat 	java.util  MatOfInt 	java.util  
MatOfPoint 	java.util  MatOfPoint2f 	java.util  Math 	java.util  Matrix 	java.util  OnnxModelHandler 	java.util  OpenCVLoader 	java.util  
OpenCVRect 	java.util  Paint 	java.util  Pair 	java.util  Point 	java.util  
ProcessorLogs 	java.util  Rect 	java.util  SEGMENTATION_TIMEOUT_SECONDS 	java.util  Scalar 	java.util  SimpleDateFormat 	java.util  Size 	java.util  String 	java.util  SubjectSegmentation 	java.util  SubjectSegmenterOptions 	java.util  System 	java.util  TAG 	java.util  
TFLiteHandler 	java.util  Tasks 	java.util  TextRecognition 	java.util  TextRecognizerOptions 	java.util  TimeUnit 	java.util  Utils 	java.util  YOLOModelHandler 	java.util  apply 	java.util  arrayOf 	java.util  average 	java.util  coerceIn 	java.util  	compareBy 	java.util  
doubleArrayOf 	java.util  filter 	java.util  forEach 	java.util  forEachIndexed 	java.util  format 	java.util  indices 	java.util  instance 	java.util  
intArrayOf 	java.util  isDigit 	java.util  
isNotEmpty 	java.util  java 	java.util  last 	java.util  let 	java.util  listOf 	java.util  map 	java.util  max 	java.util  maxOf 	java.util  minOf 	java.util  
plusAssign 	java.util  println 	java.util  sortedBy 	java.util  
sortedWith 	java.util  	substring 	java.util  take 	java.util  thenBy 	java.util  toTypedArray 	java.util  until 	java.util  use 	java.util  	withIndex 	java.util  add java.util.AbstractCollection  addAll java.util.AbstractCollection  clear java.util.AbstractCollection  get java.util.AbstractCollection  isEmpty java.util.AbstractCollection  
isNotEmpty java.util.AbstractCollection  iterator java.util.AbstractCollection  map java.util.AbstractCollection  add java.util.AbstractList  addAll java.util.AbstractList  clear java.util.AbstractList  get java.util.AbstractList  isEmpty java.util.AbstractList  
isNotEmpty java.util.AbstractList  iterator java.util.AbstractList  map java.util.AbstractList  add java.util.ArrayList  addAll java.util.ArrayList  clear java.util.ArrayList  get java.util.ArrayList  
getISNotEmpty java.util.ArrayList  
getIsNotEmpty java.util.ArrayList  getMAP java.util.ArrayList  getMap java.util.ArrayList  isEmpty java.util.ArrayList  
isNotEmpty java.util.ArrayList  iterator java.util.ArrayList  map java.util.ArrayList  size java.util.ArrayList  sort java.util.Collections  <SAM-CONSTRUCTOR> java.util.Comparator  comparingInt java.util.Comparator  	getTHENBy java.util.Comparator  	getThenBy java.util.Comparator  thenBy java.util.Comparator  get java.util.List  getMAP java.util.List  getMap java.util.List  isEmpty java.util.List  iterator java.util.List  map java.util.List  
getDefault java.util.Locale  Array java.util.concurrent  	ArrayList java.util.concurrent  AtomicReference java.util.concurrent  Bitmap java.util.concurrent  Canvas java.util.concurrent  ChequeProcessorSDK java.util.concurrent  ChequeUtils java.util.concurrent  ClockwiseComparator java.util.concurrent  Collections java.util.concurrent  Color java.util.concurrent  
Comparator java.util.concurrent  Core java.util.concurrent  CountDownLatch java.util.concurrent  CvType java.util.concurrent  Date java.util.concurrent  ExecutorService java.util.concurrent  	Executors java.util.concurrent  File java.util.concurrent  FileOutputStream java.util.concurrent  HashMap java.util.concurrent  IllegalStateException java.util.concurrent  Imgproc java.util.concurrent  
InputImage java.util.concurrent  Int java.util.concurrent  IntArray java.util.concurrent  JvmField java.util.concurrent  	JvmStatic java.util.concurrent  Locale java.util.concurrent  Log java.util.concurrent  Mat java.util.concurrent  MatOfInt java.util.concurrent  
MatOfPoint java.util.concurrent  MatOfPoint2f java.util.concurrent  Math java.util.concurrent  Matrix java.util.concurrent  OnnxModelHandler java.util.concurrent  OpenCVLoader java.util.concurrent  
OpenCVRect java.util.concurrent  Paint java.util.concurrent  Pair java.util.concurrent  Point java.util.concurrent  
ProcessorLogs java.util.concurrent  Rect java.util.concurrent  SEGMENTATION_TIMEOUT_SECONDS java.util.concurrent  Scalar java.util.concurrent  SimpleDateFormat java.util.concurrent  Size java.util.concurrent  String java.util.concurrent  SubjectSegmentation java.util.concurrent  SubjectSegmenterOptions java.util.concurrent  System java.util.concurrent  TAG java.util.concurrent  
TFLiteHandler java.util.concurrent  Tasks java.util.concurrent  TextRecognition java.util.concurrent  TextRecognizerOptions java.util.concurrent  TimeUnit java.util.concurrent  Utils java.util.concurrent  YOLOModelHandler java.util.concurrent  apply java.util.concurrent  arrayOf java.util.concurrent  average java.util.concurrent  coerceIn java.util.concurrent  	compareBy java.util.concurrent  
doubleArrayOf java.util.concurrent  filter java.util.concurrent  forEach java.util.concurrent  forEachIndexed java.util.concurrent  format java.util.concurrent  indices java.util.concurrent  instance java.util.concurrent  
intArrayOf java.util.concurrent  isDigit java.util.concurrent  
isNotEmpty java.util.concurrent  java java.util.concurrent  last java.util.concurrent  let java.util.concurrent  listOf java.util.concurrent  map java.util.concurrent  max java.util.concurrent  maxOf java.util.concurrent  minOf java.util.concurrent  
plusAssign java.util.concurrent  println java.util.concurrent  sortedBy java.util.concurrent  
sortedWith java.util.concurrent  	substring java.util.concurrent  take java.util.concurrent  thenBy java.util.concurrent  toTypedArray java.util.concurrent  until java.util.concurrent  use java.util.concurrent  	withIndex java.util.concurrent  await #java.util.concurrent.CountDownLatch  	countDown #java.util.concurrent.CountDownLatch  execute $java.util.concurrent.ExecutorService  shutdown $java.util.concurrent.ExecutorService  newSingleThreadExecutor java.util.concurrent.Executors  SECONDS java.util.concurrent.TimeUnit  AtomicReference java.util.concurrent.atomic  get +java.util.concurrent.atomic.AtomicReference  set +java.util.concurrent.atomic.AtomicReference  <SAM-CONSTRUCTOR>  java.util.function.ToIntFunction  
Collectors java.util.stream  A2iABoolean kotlin  AddressLineItem kotlin  Any kotlin  Array kotlin  	ArrayList kotlin  AtomicReference kotlin  Bitmap kotlin  
BitmapFactory kotlin  Boolean kotlin  Byte kotlin  	ByteArray kotlin  ByteArrayOutputStream kotlin  
ByteBuffer kotlin  	ByteOrder kotlin  Canvas kotlin  Char kotlin  CheckDocumentResults kotlin  
CheckInput kotlin  ChequeProcessorSDK kotlin  ChequeUtils kotlin  ClockwiseComparator kotlin  Collections kotlin  Color kotlin  
Comparator kotlin  Core kotlin  CountDownLatch kotlin  CvType kotlin  DEFAULT_MODEL_PATH kotlin  DataType kotlin  Date kotlin  Double kotlin  DoubleArray kotlin  	Exception kotlin  	Executors kotlin  File kotlin  FileChannel kotlin  FileInputStream kotlin  FileOutputStream kotlin  Float kotlin  
FloatArray kotlin  FloatBuffer kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  HashMap kotlin  
INPUT_SIZE kotlin  IllegalStateException kotlin  ImageFormat kotlin  ImageOutputFormat kotlin  Imgproc kotlin  IncompatibleClassChangeError kotlin  
InputImage kotlin  Int kotlin  IntArray kotlin  Interpreter kotlin  JvmField kotlin  	JvmStatic kotlin  KEY_CVN_CODE kotlin  KEY_CVN_RECT kotlin  
KEY_CVN_SCORE kotlin  KEY_ERROR_MESSAGE kotlin  KEY_HAS_SIGNATURE kotlin  KEY_HAS_VALID_CHEQUE_CONTOUR kotlin  KEY_IS_GENUINE kotlin  KEY_IS_SUCCESSFUL kotlin  
KEY_MICR_CODE kotlin  
KEY_MICR_RECT kotlin  KEY_MICR_SCORE kotlin  KEY_ORIGINAL_IMAGE kotlin  KEY_PROCESSED_IMAGE kotlin  KEY_SIGNATURE_RECT kotlin  KEY_SIGNATURE_SCORE kotlin  KEY_VALID_MICR_POSITION kotlin  Locale kotlin  Log kotlin  Long kotlin  	LongArray kotlin  Mat kotlin  MatOfInt kotlin  
MatOfPoint kotlin  MatOfPoint2f kotlin  Math kotlin  Matrix kotlin  MemoryImage kotlin  ModelV2 kotlin  Nothing kotlin  OnnxModelHandler kotlin  
OnnxTensor kotlin  OpenCVLoader kotlin  
OpenCVRect kotlin  OrtEnvironment kotlin  OutOfMemoryError kotlin  OutputFormat kotlin  Paint kotlin  Pair kotlin  Point kotlin  ProcessedImage kotlin  
ProcessorLogs kotlin  Random kotlin  Rect kotlin  Result kotlin  RuntimeException kotlin  SEGMENTATION_TIMEOUT_SECONDS kotlin  Scalar kotlin  SimpleDateFormat kotlin  Size kotlin  String kotlin  
StringBuilder kotlin  SubjectSegmentation kotlin  SubjectSegmenterOptions kotlin  System kotlin  TAG kotlin  
TFLiteHandler kotlin  Tasks kotlin  TensorBuffer kotlin  TextRecognition kotlin  TextRecognizerOptions kotlin  TimeUnit kotlin  Triple kotlin  Unit kotlin  Utils kotlin  VerboseDetails kotlin  YOLOModelHandler kotlin  also kotlin  android kotlin  apply kotlin  arrayOf kotlin  atan2 kotlin  average kotlin  class_names kotlin  coerceIn kotlin  	compareBy kotlin  
component1 kotlin  
component2 kotlin  	divAssign kotlin  
doubleArrayOf kotlin  	emptyList kotlin  filter kotlin  floatArrayOf kotlin  forEach kotlin  forEachIndexed kotlin  format kotlin  	getOrNull kotlin  indices kotlin  instance kotlin  
intArrayOf kotlin  isDigit kotlin  
isNotEmpty kotlin  java kotlin  	javaClass kotlin  kotlin kotlin  last kotlin  let kotlin  listOf kotlin  longArrayOf kotlin  map kotlin  mapOf kotlin  max kotlin  maxOf kotlin  	maxOrNull kotlin  minOf kotlin  	minOrNull kotlin  
mutableListOf kotlin  mutableMapOf kotlin  
plusAssign kotlin  pow kotlin  println kotlin  result kotlin  run kotlin  set kotlin  sortedBy kotlin  sortedByDescending kotlin  
sortedWith kotlin  sqrt kotlin  stackTraceToString kotlin  	substring kotlin  take kotlin  thenBy kotlin  to kotlin  toTypedArray kotlin  
trimIndent kotlin  until kotlin  use kotlin  	withIndex kotlin  getFOREachIndexed kotlin.ByteArray  getForEachIndexed kotlin.ByteArray  
getISDigit kotlin.Char  
getIsDigit kotlin.Char  isDigit kotlin.Char  getCOERCEIn 
kotlin.Double  getCoerceIn 
kotlin.Double  
getPLUSAssign 
kotlin.Double  getPOW 
kotlin.Double  
getPlusAssign 
kotlin.Double  getPow 
kotlin.Double  getDIVAssign kotlin.Float  getDivAssign kotlin.Float  
getPLUSAssign kotlin.Float  
getPlusAssign kotlin.Float  
getINDICES kotlin.FloatArray  
getIndices kotlin.FloatArray  getMAXOrNull kotlin.FloatArray  getMINOrNull kotlin.FloatArray  getMaxOrNull kotlin.FloatArray  getMinOrNull kotlin.FloatArray  getTAKE kotlin.FloatArray  getTake kotlin.FloatArray  getCOERCEIn 
kotlin.Int  getCoerceIn 
kotlin.Int  getTO 
kotlin.Int  getTo 
kotlin.Int  getUNTIL 
kotlin.Int  getUntil 
kotlin.Int  
getINDICES kotlin.IntArray  
getIndices kotlin.IntArray  
component1 kotlin.Pair  
component2 kotlin.Pair  	getFILTER 
kotlin.String  	getFORMAT 
kotlin.String  	getFilter 
kotlin.String  	getFormat 
kotlin.String  
getISNotEmpty 
kotlin.String  
getIsNotEmpty 
kotlin.String  getLAST 
kotlin.String  getLast 
kotlin.String  
getPLUSAssign 
kotlin.String  
getPlusAssign 
kotlin.String  getSUBSTRING 
kotlin.String  getSubstring 
kotlin.String  getTO 
kotlin.String  
getTRIMIndent 
kotlin.String  getTo 
kotlin.String  
getTrimIndent 
kotlin.String  
isNotEmpty 
kotlin.String  	getFORMAT kotlin.String.Companion  	getFormat kotlin.String.Companion  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  second 
kotlin.Triple  third 
kotlin.Triple  A2iABoolean kotlin.annotation  AddressLineItem kotlin.annotation  Array kotlin.annotation  	ArrayList kotlin.annotation  AtomicReference kotlin.annotation  Bitmap kotlin.annotation  
BitmapFactory kotlin.annotation  	ByteArray kotlin.annotation  ByteArrayOutputStream kotlin.annotation  
ByteBuffer kotlin.annotation  	ByteOrder kotlin.annotation  Canvas kotlin.annotation  CheckDocumentResults kotlin.annotation  
CheckInput kotlin.annotation  ChequeProcessorSDK kotlin.annotation  ChequeUtils kotlin.annotation  ClockwiseComparator kotlin.annotation  Collections kotlin.annotation  Color kotlin.annotation  
Comparator kotlin.annotation  Core kotlin.annotation  CountDownLatch kotlin.annotation  CvType kotlin.annotation  DEFAULT_MODEL_PATH kotlin.annotation  DataType kotlin.annotation  Date kotlin.annotation  	Exception kotlin.annotation  	Executors kotlin.annotation  File kotlin.annotation  FileChannel kotlin.annotation  FileInputStream kotlin.annotation  FileOutputStream kotlin.annotation  
FloatArray kotlin.annotation  FloatBuffer kotlin.annotation  HashMap kotlin.annotation  
INPUT_SIZE kotlin.annotation  IllegalStateException kotlin.annotation  ImageFormat kotlin.annotation  ImageOutputFormat kotlin.annotation  Imgproc kotlin.annotation  IncompatibleClassChangeError kotlin.annotation  
InputImage kotlin.annotation  Int kotlin.annotation  IntArray kotlin.annotation  Interpreter kotlin.annotation  JvmField kotlin.annotation  	JvmStatic kotlin.annotation  KEY_CVN_CODE kotlin.annotation  KEY_CVN_RECT kotlin.annotation  
KEY_CVN_SCORE kotlin.annotation  KEY_ERROR_MESSAGE kotlin.annotation  KEY_HAS_SIGNATURE kotlin.annotation  KEY_HAS_VALID_CHEQUE_CONTOUR kotlin.annotation  KEY_IS_GENUINE kotlin.annotation  KEY_IS_SUCCESSFUL kotlin.annotation  
KEY_MICR_CODE kotlin.annotation  
KEY_MICR_RECT kotlin.annotation  KEY_MICR_SCORE kotlin.annotation  KEY_ORIGINAL_IMAGE kotlin.annotation  KEY_PROCESSED_IMAGE kotlin.annotation  KEY_SIGNATURE_RECT kotlin.annotation  KEY_SIGNATURE_SCORE kotlin.annotation  KEY_VALID_MICR_POSITION kotlin.annotation  Locale kotlin.annotation  Log kotlin.annotation  Mat kotlin.annotation  MatOfInt kotlin.annotation  
MatOfPoint kotlin.annotation  MatOfPoint2f kotlin.annotation  Math kotlin.annotation  Matrix kotlin.annotation  MemoryImage kotlin.annotation  ModelV2 kotlin.annotation  OnnxModelHandler kotlin.annotation  
OnnxTensor kotlin.annotation  OpenCVLoader kotlin.annotation  
OpenCVRect kotlin.annotation  OrtEnvironment kotlin.annotation  OutOfMemoryError kotlin.annotation  OutputFormat kotlin.annotation  Paint kotlin.annotation  Pair kotlin.annotation  Point kotlin.annotation  ProcessedImage kotlin.annotation  
ProcessorLogs kotlin.annotation  Random kotlin.annotation  Rect kotlin.annotation  Result kotlin.annotation  RuntimeException kotlin.annotation  SEGMENTATION_TIMEOUT_SECONDS kotlin.annotation  Scalar kotlin.annotation  SimpleDateFormat kotlin.annotation  Size kotlin.annotation  String kotlin.annotation  
StringBuilder kotlin.annotation  SubjectSegmentation kotlin.annotation  SubjectSegmenterOptions kotlin.annotation  System kotlin.annotation  TAG kotlin.annotation  
TFLiteHandler kotlin.annotation  Tasks kotlin.annotation  TensorBuffer kotlin.annotation  TextRecognition kotlin.annotation  TextRecognizerOptions kotlin.annotation  TimeUnit kotlin.annotation  Triple kotlin.annotation  Utils kotlin.annotation  VerboseDetails kotlin.annotation  YOLOModelHandler kotlin.annotation  also kotlin.annotation  android kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  atan2 kotlin.annotation  average kotlin.annotation  class_names kotlin.annotation  coerceIn kotlin.annotation  	compareBy kotlin.annotation  
component1 kotlin.annotation  
component2 kotlin.annotation  	divAssign kotlin.annotation  
doubleArrayOf kotlin.annotation  	emptyList kotlin.annotation  filter kotlin.annotation  floatArrayOf kotlin.annotation  forEach kotlin.annotation  forEachIndexed kotlin.annotation  format kotlin.annotation  	getOrNull kotlin.annotation  indices kotlin.annotation  instance kotlin.annotation  
intArrayOf kotlin.annotation  isDigit kotlin.annotation  
isNotEmpty kotlin.annotation  java kotlin.annotation  	javaClass kotlin.annotation  kotlin kotlin.annotation  last kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  longArrayOf kotlin.annotation  map kotlin.annotation  mapOf kotlin.annotation  max kotlin.annotation  maxOf kotlin.annotation  	maxOrNull kotlin.annotation  minOf kotlin.annotation  	minOrNull kotlin.annotation  
mutableListOf kotlin.annotation  mutableMapOf kotlin.annotation  
plusAssign kotlin.annotation  pow kotlin.annotation  println kotlin.annotation  result kotlin.annotation  run kotlin.annotation  set kotlin.annotation  sortedBy kotlin.annotation  sortedByDescending kotlin.annotation  
sortedWith kotlin.annotation  sqrt kotlin.annotation  stackTraceToString kotlin.annotation  	substring kotlin.annotation  take kotlin.annotation  thenBy kotlin.annotation  to kotlin.annotation  toTypedArray kotlin.annotation  
trimIndent kotlin.annotation  until kotlin.annotation  use kotlin.annotation  	withIndex kotlin.annotation  A2iABoolean kotlin.collections  AddressLineItem kotlin.collections  Array kotlin.collections  	ArrayList kotlin.collections  AtomicReference kotlin.collections  Bitmap kotlin.collections  
BitmapFactory kotlin.collections  	ByteArray kotlin.collections  ByteArrayOutputStream kotlin.collections  
ByteBuffer kotlin.collections  	ByteOrder kotlin.collections  Canvas kotlin.collections  CheckDocumentResults kotlin.collections  
CheckInput kotlin.collections  ChequeProcessorSDK kotlin.collections  ChequeUtils kotlin.collections  ClockwiseComparator kotlin.collections  Collections kotlin.collections  Color kotlin.collections  
Comparator kotlin.collections  Core kotlin.collections  CountDownLatch kotlin.collections  CvType kotlin.collections  DEFAULT_MODEL_PATH kotlin.collections  DataType kotlin.collections  Date kotlin.collections  	Exception kotlin.collections  	Executors kotlin.collections  File kotlin.collections  FileChannel kotlin.collections  FileInputStream kotlin.collections  FileOutputStream kotlin.collections  
FloatArray kotlin.collections  FloatBuffer kotlin.collections  HashMap kotlin.collections  
INPUT_SIZE kotlin.collections  IllegalStateException kotlin.collections  ImageFormat kotlin.collections  ImageOutputFormat kotlin.collections  Imgproc kotlin.collections  IncompatibleClassChangeError kotlin.collections  IndexedValue kotlin.collections  
InputImage kotlin.collections  Int kotlin.collections  IntArray kotlin.collections  Interpreter kotlin.collections  Iterable kotlin.collections  JvmField kotlin.collections  	JvmStatic kotlin.collections  KEY_CVN_CODE kotlin.collections  KEY_CVN_RECT kotlin.collections  
KEY_CVN_SCORE kotlin.collections  KEY_ERROR_MESSAGE kotlin.collections  KEY_HAS_SIGNATURE kotlin.collections  KEY_HAS_VALID_CHEQUE_CONTOUR kotlin.collections  KEY_IS_GENUINE kotlin.collections  KEY_IS_SUCCESSFUL kotlin.collections  
KEY_MICR_CODE kotlin.collections  
KEY_MICR_RECT kotlin.collections  KEY_MICR_SCORE kotlin.collections  KEY_ORIGINAL_IMAGE kotlin.collections  KEY_PROCESSED_IMAGE kotlin.collections  KEY_SIGNATURE_RECT kotlin.collections  KEY_SIGNATURE_SCORE kotlin.collections  KEY_VALID_MICR_POSITION kotlin.collections  List kotlin.collections  Locale kotlin.collections  Log kotlin.collections  Map kotlin.collections  Mat kotlin.collections  MatOfInt kotlin.collections  
MatOfPoint kotlin.collections  MatOfPoint2f kotlin.collections  Math kotlin.collections  Matrix kotlin.collections  MemoryImage kotlin.collections  ModelV2 kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  OnnxModelHandler kotlin.collections  
OnnxTensor kotlin.collections  OpenCVLoader kotlin.collections  
OpenCVRect kotlin.collections  OrtEnvironment kotlin.collections  OutOfMemoryError kotlin.collections  OutputFormat kotlin.collections  Paint kotlin.collections  Pair kotlin.collections  Point kotlin.collections  ProcessedImage kotlin.collections  
ProcessorLogs kotlin.collections  Random kotlin.collections  Rect kotlin.collections  Result kotlin.collections  RuntimeException kotlin.collections  SEGMENTATION_TIMEOUT_SECONDS kotlin.collections  Scalar kotlin.collections  Set kotlin.collections  SimpleDateFormat kotlin.collections  Size kotlin.collections  String kotlin.collections  
StringBuilder kotlin.collections  SubjectSegmentation kotlin.collections  SubjectSegmenterOptions kotlin.collections  System kotlin.collections  TAG kotlin.collections  
TFLiteHandler kotlin.collections  Tasks kotlin.collections  TensorBuffer kotlin.collections  TextRecognition kotlin.collections  TextRecognizerOptions kotlin.collections  TimeUnit kotlin.collections  Triple kotlin.collections  Utils kotlin.collections  VerboseDetails kotlin.collections  YOLOModelHandler kotlin.collections  also kotlin.collections  android kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  atan2 kotlin.collections  average kotlin.collections  class_names kotlin.collections  coerceIn kotlin.collections  	compareBy kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  	divAssign kotlin.collections  
doubleArrayOf kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  floatArrayOf kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  format kotlin.collections  	getOrNull kotlin.collections  indices kotlin.collections  instance kotlin.collections  
intArrayOf kotlin.collections  isDigit kotlin.collections  
isNotEmpty kotlin.collections  java kotlin.collections  	javaClass kotlin.collections  kotlin kotlin.collections  last kotlin.collections  let kotlin.collections  listOf kotlin.collections  longArrayOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  max kotlin.collections  maxOf kotlin.collections  	maxOrNull kotlin.collections  minOf kotlin.collections  	minOrNull kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  
plusAssign kotlin.collections  pow kotlin.collections  println kotlin.collections  result kotlin.collections  run kotlin.collections  set kotlin.collections  sortedBy kotlin.collections  sortedByDescending kotlin.collections  
sortedWith kotlin.collections  sqrt kotlin.collections  stackTraceToString kotlin.collections  	substring kotlin.collections  take kotlin.collections  thenBy kotlin.collections  to kotlin.collections  toTypedArray kotlin.collections  
trimIndent kotlin.collections  until kotlin.collections  use kotlin.collections  	withIndex kotlin.collections  
component1 kotlin.collections.IndexedValue  
component2 kotlin.collections.IndexedValue  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  
getAVERAGE kotlin.collections.List  
getAverage kotlin.collections.List  	getFILTER kotlin.collections.List  getFOREachIndexed kotlin.collections.List  	getFilter kotlin.collections.List  getForEachIndexed kotlin.collections.List  
getINDICES kotlin.collections.List  
getISNotEmpty kotlin.collections.List  
getIndices kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  getSORTEDBy kotlin.collections.List  getSORTEDByDescending kotlin.collections.List  getSortedBy kotlin.collections.List  getSortedByDescending kotlin.collections.List  getTAKE kotlin.collections.List  getTOTypedArray kotlin.collections.List  getTake kotlin.collections.List  getToTypedArray kotlin.collections.List  getWITHIndex kotlin.collections.List  getWithIndex kotlin.collections.List  
isNotEmpty kotlin.collections.List  Entry kotlin.collections.Map  
getComponent1 kotlin.collections.Map.Entry  
getComponent2 kotlin.collections.Map.Entry  getFOREachIndexed kotlin.collections.MutableList  getForEachIndexed kotlin.collections.MutableList  getGETOrNull kotlin.collections.MutableList  getGetOrNull kotlin.collections.MutableList  
getSORTEDWith kotlin.collections.MutableList  
getSortedWith kotlin.collections.MutableList  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  A2iABoolean kotlin.comparisons  AddressLineItem kotlin.comparisons  Array kotlin.comparisons  	ArrayList kotlin.comparisons  AtomicReference kotlin.comparisons  Bitmap kotlin.comparisons  
BitmapFactory kotlin.comparisons  	ByteArray kotlin.comparisons  ByteArrayOutputStream kotlin.comparisons  
ByteBuffer kotlin.comparisons  	ByteOrder kotlin.comparisons  Canvas kotlin.comparisons  CheckDocumentResults kotlin.comparisons  
CheckInput kotlin.comparisons  ChequeProcessorSDK kotlin.comparisons  ChequeUtils kotlin.comparisons  ClockwiseComparator kotlin.comparisons  Collections kotlin.comparisons  Color kotlin.comparisons  
Comparator kotlin.comparisons  Core kotlin.comparisons  CountDownLatch kotlin.comparisons  CvType kotlin.comparisons  DEFAULT_MODEL_PATH kotlin.comparisons  DataType kotlin.comparisons  Date kotlin.comparisons  	Exception kotlin.comparisons  	Executors kotlin.comparisons  File kotlin.comparisons  FileChannel kotlin.comparisons  FileInputStream kotlin.comparisons  FileOutputStream kotlin.comparisons  
FloatArray kotlin.comparisons  FloatBuffer kotlin.comparisons  HashMap kotlin.comparisons  
INPUT_SIZE kotlin.comparisons  IllegalStateException kotlin.comparisons  ImageFormat kotlin.comparisons  ImageOutputFormat kotlin.comparisons  Imgproc kotlin.comparisons  IncompatibleClassChangeError kotlin.comparisons  
InputImage kotlin.comparisons  Int kotlin.comparisons  IntArray kotlin.comparisons  Interpreter kotlin.comparisons  JvmField kotlin.comparisons  	JvmStatic kotlin.comparisons  KEY_CVN_CODE kotlin.comparisons  KEY_CVN_RECT kotlin.comparisons  
KEY_CVN_SCORE kotlin.comparisons  KEY_ERROR_MESSAGE kotlin.comparisons  KEY_HAS_SIGNATURE kotlin.comparisons  KEY_HAS_VALID_CHEQUE_CONTOUR kotlin.comparisons  KEY_IS_GENUINE kotlin.comparisons  KEY_IS_SUCCESSFUL kotlin.comparisons  
KEY_MICR_CODE kotlin.comparisons  
KEY_MICR_RECT kotlin.comparisons  KEY_MICR_SCORE kotlin.comparisons  KEY_ORIGINAL_IMAGE kotlin.comparisons  KEY_PROCESSED_IMAGE kotlin.comparisons  KEY_SIGNATURE_RECT kotlin.comparisons  KEY_SIGNATURE_SCORE kotlin.comparisons  KEY_VALID_MICR_POSITION kotlin.comparisons  Locale kotlin.comparisons  Log kotlin.comparisons  Mat kotlin.comparisons  MatOfInt kotlin.comparisons  
MatOfPoint kotlin.comparisons  MatOfPoint2f kotlin.comparisons  Math kotlin.comparisons  Matrix kotlin.comparisons  MemoryImage kotlin.comparisons  ModelV2 kotlin.comparisons  OnnxModelHandler kotlin.comparisons  
OnnxTensor kotlin.comparisons  OpenCVLoader kotlin.comparisons  
OpenCVRect kotlin.comparisons  OrtEnvironment kotlin.comparisons  OutOfMemoryError kotlin.comparisons  OutputFormat kotlin.comparisons  Paint kotlin.comparisons  Pair kotlin.comparisons  Point kotlin.comparisons  ProcessedImage kotlin.comparisons  
ProcessorLogs kotlin.comparisons  Random kotlin.comparisons  Rect kotlin.comparisons  Result kotlin.comparisons  RuntimeException kotlin.comparisons  SEGMENTATION_TIMEOUT_SECONDS kotlin.comparisons  Scalar kotlin.comparisons  SimpleDateFormat kotlin.comparisons  Size kotlin.comparisons  String kotlin.comparisons  
StringBuilder kotlin.comparisons  SubjectSegmentation kotlin.comparisons  SubjectSegmenterOptions kotlin.comparisons  System kotlin.comparisons  TAG kotlin.comparisons  
TFLiteHandler kotlin.comparisons  Tasks kotlin.comparisons  TensorBuffer kotlin.comparisons  TextRecognition kotlin.comparisons  TextRecognizerOptions kotlin.comparisons  TimeUnit kotlin.comparisons  Triple kotlin.comparisons  Utils kotlin.comparisons  VerboseDetails kotlin.comparisons  YOLOModelHandler kotlin.comparisons  also kotlin.comparisons  android kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  atan2 kotlin.comparisons  average kotlin.comparisons  class_names kotlin.comparisons  coerceIn kotlin.comparisons  	compareBy kotlin.comparisons  
component1 kotlin.comparisons  
component2 kotlin.comparisons  	divAssign kotlin.comparisons  
doubleArrayOf kotlin.comparisons  	emptyList kotlin.comparisons  filter kotlin.comparisons  floatArrayOf kotlin.comparisons  forEach kotlin.comparisons  forEachIndexed kotlin.comparisons  format kotlin.comparisons  	getOrNull kotlin.comparisons  indices kotlin.comparisons  instance kotlin.comparisons  
intArrayOf kotlin.comparisons  isDigit kotlin.comparisons  
isNotEmpty kotlin.comparisons  java kotlin.comparisons  	javaClass kotlin.comparisons  kotlin kotlin.comparisons  last kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  longArrayOf kotlin.comparisons  map kotlin.comparisons  mapOf kotlin.comparisons  max kotlin.comparisons  maxOf kotlin.comparisons  	maxOrNull kotlin.comparisons  minOf kotlin.comparisons  	minOrNull kotlin.comparisons  
mutableListOf kotlin.comparisons  mutableMapOf kotlin.comparisons  
plusAssign kotlin.comparisons  pow kotlin.comparisons  println kotlin.comparisons  result kotlin.comparisons  run kotlin.comparisons  set kotlin.comparisons  sortedBy kotlin.comparisons  sortedByDescending kotlin.comparisons  
sortedWith kotlin.comparisons  sqrt kotlin.comparisons  stackTraceToString kotlin.comparisons  	substring kotlin.comparisons  take kotlin.comparisons  thenBy kotlin.comparisons  to kotlin.comparisons  toTypedArray kotlin.comparisons  
trimIndent kotlin.comparisons  until kotlin.comparisons  use kotlin.comparisons  	withIndex kotlin.comparisons  A2iABoolean 	kotlin.io  AddressLineItem 	kotlin.io  Array 	kotlin.io  	ArrayList 	kotlin.io  AtomicReference 	kotlin.io  Bitmap 	kotlin.io  
BitmapFactory 	kotlin.io  	ByteArray 	kotlin.io  ByteArrayOutputStream 	kotlin.io  
ByteBuffer 	kotlin.io  	ByteOrder 	kotlin.io  Canvas 	kotlin.io  CheckDocumentResults 	kotlin.io  
CheckInput 	kotlin.io  ChequeProcessorSDK 	kotlin.io  ChequeUtils 	kotlin.io  ClockwiseComparator 	kotlin.io  Collections 	kotlin.io  Color 	kotlin.io  
Comparator 	kotlin.io  Core 	kotlin.io  CountDownLatch 	kotlin.io  CvType 	kotlin.io  DEFAULT_MODEL_PATH 	kotlin.io  DataType 	kotlin.io  Date 	kotlin.io  	Exception 	kotlin.io  	Executors 	kotlin.io  File 	kotlin.io  FileChannel 	kotlin.io  FileInputStream 	kotlin.io  FileOutputStream 	kotlin.io  
FloatArray 	kotlin.io  FloatBuffer 	kotlin.io  HashMap 	kotlin.io  
INPUT_SIZE 	kotlin.io  IllegalStateException 	kotlin.io  ImageFormat 	kotlin.io  ImageOutputFormat 	kotlin.io  Imgproc 	kotlin.io  IncompatibleClassChangeError 	kotlin.io  
InputImage 	kotlin.io  Int 	kotlin.io  IntArray 	kotlin.io  Interpreter 	kotlin.io  JvmField 	kotlin.io  	JvmStatic 	kotlin.io  KEY_CVN_CODE 	kotlin.io  KEY_CVN_RECT 	kotlin.io  
KEY_CVN_SCORE 	kotlin.io  KEY_ERROR_MESSAGE 	kotlin.io  KEY_HAS_SIGNATURE 	kotlin.io  KEY_HAS_VALID_CHEQUE_CONTOUR 	kotlin.io  KEY_IS_GENUINE 	kotlin.io  KEY_IS_SUCCESSFUL 	kotlin.io  
KEY_MICR_CODE 	kotlin.io  
KEY_MICR_RECT 	kotlin.io  KEY_MICR_SCORE 	kotlin.io  KEY_ORIGINAL_IMAGE 	kotlin.io  KEY_PROCESSED_IMAGE 	kotlin.io  KEY_SIGNATURE_RECT 	kotlin.io  KEY_SIGNATURE_SCORE 	kotlin.io  KEY_VALID_MICR_POSITION 	kotlin.io  Locale 	kotlin.io  Log 	kotlin.io  Mat 	kotlin.io  MatOfInt 	kotlin.io  
MatOfPoint 	kotlin.io  MatOfPoint2f 	kotlin.io  Math 	kotlin.io  Matrix 	kotlin.io  MemoryImage 	kotlin.io  ModelV2 	kotlin.io  OnnxModelHandler 	kotlin.io  
OnnxTensor 	kotlin.io  OpenCVLoader 	kotlin.io  
OpenCVRect 	kotlin.io  OrtEnvironment 	kotlin.io  OutOfMemoryError 	kotlin.io  OutputFormat 	kotlin.io  Paint 	kotlin.io  Pair 	kotlin.io  Point 	kotlin.io  ProcessedImage 	kotlin.io  
ProcessorLogs 	kotlin.io  Random 	kotlin.io  Rect 	kotlin.io  Result 	kotlin.io  RuntimeException 	kotlin.io  SEGMENTATION_TIMEOUT_SECONDS 	kotlin.io  Scalar 	kotlin.io  SimpleDateFormat 	kotlin.io  Size 	kotlin.io  String 	kotlin.io  
StringBuilder 	kotlin.io  SubjectSegmentation 	kotlin.io  SubjectSegmenterOptions 	kotlin.io  System 	kotlin.io  TAG 	kotlin.io  
TFLiteHandler 	kotlin.io  Tasks 	kotlin.io  TensorBuffer 	kotlin.io  TextRecognition 	kotlin.io  TextRecognizerOptions 	kotlin.io  TimeUnit 	kotlin.io  Triple 	kotlin.io  Utils 	kotlin.io  VerboseDetails 	kotlin.io  YOLOModelHandler 	kotlin.io  also 	kotlin.io  android 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  atan2 	kotlin.io  average 	kotlin.io  class_names 	kotlin.io  coerceIn 	kotlin.io  	compareBy 	kotlin.io  
component1 	kotlin.io  
component2 	kotlin.io  	divAssign 	kotlin.io  
doubleArrayOf 	kotlin.io  	emptyList 	kotlin.io  filter 	kotlin.io  floatArrayOf 	kotlin.io  forEach 	kotlin.io  forEachIndexed 	kotlin.io  format 	kotlin.io  	getOrNull 	kotlin.io  indices 	kotlin.io  instance 	kotlin.io  
intArrayOf 	kotlin.io  isDigit 	kotlin.io  
isNotEmpty 	kotlin.io  java 	kotlin.io  	javaClass 	kotlin.io  kotlin 	kotlin.io  last 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  longArrayOf 	kotlin.io  map 	kotlin.io  mapOf 	kotlin.io  max 	kotlin.io  maxOf 	kotlin.io  	maxOrNull 	kotlin.io  minOf 	kotlin.io  	minOrNull 	kotlin.io  
mutableListOf 	kotlin.io  mutableMapOf 	kotlin.io  
plusAssign 	kotlin.io  pow 	kotlin.io  println 	kotlin.io  result 	kotlin.io  run 	kotlin.io  set 	kotlin.io  sortedBy 	kotlin.io  sortedByDescending 	kotlin.io  
sortedWith 	kotlin.io  sqrt 	kotlin.io  stackTraceToString 	kotlin.io  	substring 	kotlin.io  take 	kotlin.io  thenBy 	kotlin.io  to 	kotlin.io  toTypedArray 	kotlin.io  
trimIndent 	kotlin.io  until 	kotlin.io  use 	kotlin.io  	withIndex 	kotlin.io  A2iABoolean 
kotlin.jvm  AddressLineItem 
kotlin.jvm  Array 
kotlin.jvm  	ArrayList 
kotlin.jvm  AtomicReference 
kotlin.jvm  Bitmap 
kotlin.jvm  
BitmapFactory 
kotlin.jvm  	ByteArray 
kotlin.jvm  ByteArrayOutputStream 
kotlin.jvm  
ByteBuffer 
kotlin.jvm  	ByteOrder 
kotlin.jvm  Canvas 
kotlin.jvm  CheckDocumentResults 
kotlin.jvm  
CheckInput 
kotlin.jvm  ChequeProcessorSDK 
kotlin.jvm  ChequeUtils 
kotlin.jvm  ClockwiseComparator 
kotlin.jvm  Collections 
kotlin.jvm  Color 
kotlin.jvm  
Comparator 
kotlin.jvm  Core 
kotlin.jvm  CountDownLatch 
kotlin.jvm  CvType 
kotlin.jvm  DEFAULT_MODEL_PATH 
kotlin.jvm  DataType 
kotlin.jvm  Date 
kotlin.jvm  	Exception 
kotlin.jvm  	Executors 
kotlin.jvm  File 
kotlin.jvm  FileChannel 
kotlin.jvm  FileInputStream 
kotlin.jvm  FileOutputStream 
kotlin.jvm  
FloatArray 
kotlin.jvm  FloatBuffer 
kotlin.jvm  HashMap 
kotlin.jvm  
INPUT_SIZE 
kotlin.jvm  IllegalStateException 
kotlin.jvm  ImageFormat 
kotlin.jvm  ImageOutputFormat 
kotlin.jvm  Imgproc 
kotlin.jvm  IncompatibleClassChangeError 
kotlin.jvm  
InputImage 
kotlin.jvm  Int 
kotlin.jvm  IntArray 
kotlin.jvm  Interpreter 
kotlin.jvm  JvmField 
kotlin.jvm  	JvmStatic 
kotlin.jvm  KEY_CVN_CODE 
kotlin.jvm  KEY_CVN_RECT 
kotlin.jvm  
KEY_CVN_SCORE 
kotlin.jvm  KEY_ERROR_MESSAGE 
kotlin.jvm  KEY_HAS_SIGNATURE 
kotlin.jvm  KEY_HAS_VALID_CHEQUE_CONTOUR 
kotlin.jvm  KEY_IS_GENUINE 
kotlin.jvm  KEY_IS_SUCCESSFUL 
kotlin.jvm  
KEY_MICR_CODE 
kotlin.jvm  
KEY_MICR_RECT 
kotlin.jvm  KEY_MICR_SCORE 
kotlin.jvm  KEY_ORIGINAL_IMAGE 
kotlin.jvm  KEY_PROCESSED_IMAGE 
kotlin.jvm  KEY_SIGNATURE_RECT 
kotlin.jvm  KEY_SIGNATURE_SCORE 
kotlin.jvm  KEY_VALID_MICR_POSITION 
kotlin.jvm  Locale 
kotlin.jvm  Log 
kotlin.jvm  Mat 
kotlin.jvm  MatOfInt 
kotlin.jvm  
MatOfPoint 
kotlin.jvm  MatOfPoint2f 
kotlin.jvm  Math 
kotlin.jvm  Matrix 
kotlin.jvm  MemoryImage 
kotlin.jvm  ModelV2 
kotlin.jvm  OnnxModelHandler 
kotlin.jvm  
OnnxTensor 
kotlin.jvm  OpenCVLoader 
kotlin.jvm  
OpenCVRect 
kotlin.jvm  OrtEnvironment 
kotlin.jvm  OutOfMemoryError 
kotlin.jvm  OutputFormat 
kotlin.jvm  Paint 
kotlin.jvm  Pair 
kotlin.jvm  Point 
kotlin.jvm  ProcessedImage 
kotlin.jvm  
ProcessorLogs 
kotlin.jvm  Random 
kotlin.jvm  Rect 
kotlin.jvm  Result 
kotlin.jvm  RuntimeException 
kotlin.jvm  SEGMENTATION_TIMEOUT_SECONDS 
kotlin.jvm  Scalar 
kotlin.jvm  SimpleDateFormat 
kotlin.jvm  Size 
kotlin.jvm  String 
kotlin.jvm  
StringBuilder 
kotlin.jvm  SubjectSegmentation 
kotlin.jvm  SubjectSegmenterOptions 
kotlin.jvm  System 
kotlin.jvm  TAG 
kotlin.jvm  
TFLiteHandler 
kotlin.jvm  Tasks 
kotlin.jvm  TensorBuffer 
kotlin.jvm  TextRecognition 
kotlin.jvm  TextRecognizerOptions 
kotlin.jvm  TimeUnit 
kotlin.jvm  Triple 
kotlin.jvm  Utils 
kotlin.jvm  VerboseDetails 
kotlin.jvm  YOLOModelHandler 
kotlin.jvm  also 
kotlin.jvm  android 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  atan2 
kotlin.jvm  average 
kotlin.jvm  class_names 
kotlin.jvm  coerceIn 
kotlin.jvm  	compareBy 
kotlin.jvm  
component1 
kotlin.jvm  
component2 
kotlin.jvm  	divAssign 
kotlin.jvm  
doubleArrayOf 
kotlin.jvm  	emptyList 
kotlin.jvm  filter 
kotlin.jvm  floatArrayOf 
kotlin.jvm  forEach 
kotlin.jvm  forEachIndexed 
kotlin.jvm  format 
kotlin.jvm  	getOrNull 
kotlin.jvm  indices 
kotlin.jvm  instance 
kotlin.jvm  
intArrayOf 
kotlin.jvm  isDigit 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  java 
kotlin.jvm  	javaClass 
kotlin.jvm  kotlin 
kotlin.jvm  last 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  longArrayOf 
kotlin.jvm  map 
kotlin.jvm  mapOf 
kotlin.jvm  max 
kotlin.jvm  maxOf 
kotlin.jvm  	maxOrNull 
kotlin.jvm  minOf 
kotlin.jvm  	minOrNull 
kotlin.jvm  
mutableListOf 
kotlin.jvm  mutableMapOf 
kotlin.jvm  
plusAssign 
kotlin.jvm  pow 
kotlin.jvm  println 
kotlin.jvm  result 
kotlin.jvm  run 
kotlin.jvm  set 
kotlin.jvm  sortedBy 
kotlin.jvm  sortedByDescending 
kotlin.jvm  
sortedWith 
kotlin.jvm  sqrt 
kotlin.jvm  stackTraceToString 
kotlin.jvm  	substring 
kotlin.jvm  take 
kotlin.jvm  thenBy 
kotlin.jvm  to 
kotlin.jvm  toTypedArray 
kotlin.jvm  
trimIndent 
kotlin.jvm  until 
kotlin.jvm  use 
kotlin.jvm  	withIndex 
kotlin.jvm  Array kotlin.math  	ArrayList kotlin.math  AtomicReference kotlin.math  Bitmap kotlin.math  Canvas kotlin.math  ChequeProcessorSDK kotlin.math  ChequeUtils kotlin.math  ClockwiseComparator kotlin.math  Collections kotlin.math  Color kotlin.math  
Comparator kotlin.math  Core kotlin.math  CountDownLatch kotlin.math  CvType kotlin.math  Date kotlin.math  	Executors kotlin.math  File kotlin.math  FileOutputStream kotlin.math  HashMap kotlin.math  IllegalStateException kotlin.math  Imgproc kotlin.math  
InputImage kotlin.math  Int kotlin.math  IntArray kotlin.math  JvmField kotlin.math  	JvmStatic kotlin.math  Locale kotlin.math  Log kotlin.math  Mat kotlin.math  MatOfInt kotlin.math  
MatOfPoint kotlin.math  MatOfPoint2f kotlin.math  Math kotlin.math  Matrix kotlin.math  OnnxModelHandler kotlin.math  OpenCVLoader kotlin.math  
OpenCVRect kotlin.math  Paint kotlin.math  Pair kotlin.math  Point kotlin.math  
ProcessorLogs kotlin.math  Rect kotlin.math  SEGMENTATION_TIMEOUT_SECONDS kotlin.math  Scalar kotlin.math  SimpleDateFormat kotlin.math  Size kotlin.math  String kotlin.math  SubjectSegmentation kotlin.math  SubjectSegmenterOptions kotlin.math  System kotlin.math  TAG kotlin.math  
TFLiteHandler kotlin.math  Tasks kotlin.math  TextRecognition kotlin.math  TextRecognizerOptions kotlin.math  TimeUnit kotlin.math  Utils kotlin.math  YOLOModelHandler kotlin.math  apply kotlin.math  arrayOf kotlin.math  atan2 kotlin.math  average kotlin.math  coerceIn kotlin.math  	compareBy kotlin.math  
doubleArrayOf kotlin.math  exp kotlin.math  filter kotlin.math  forEach kotlin.math  forEachIndexed kotlin.math  format kotlin.math  indices kotlin.math  instance kotlin.math  
intArrayOf kotlin.math  isDigit kotlin.math  
isNotEmpty kotlin.math  java kotlin.math  last kotlin.math  let kotlin.math  listOf kotlin.math  map kotlin.math  max kotlin.math  maxOf kotlin.math  minOf kotlin.math  
plusAssign kotlin.math  pow kotlin.math  println kotlin.math  sortedBy kotlin.math  
sortedWith kotlin.math  sqrt kotlin.math  	substring kotlin.math  take kotlin.math  thenBy kotlin.math  toTypedArray kotlin.math  until kotlin.math  use kotlin.math  	withIndex kotlin.math  Random 
kotlin.random  	nextFloat kotlin.random.Random  	nextFloat kotlin.random.Random.Default  A2iABoolean 
kotlin.ranges  AddressLineItem 
kotlin.ranges  Array 
kotlin.ranges  	ArrayList 
kotlin.ranges  AtomicReference 
kotlin.ranges  Bitmap 
kotlin.ranges  
BitmapFactory 
kotlin.ranges  	ByteArray 
kotlin.ranges  ByteArrayOutputStream 
kotlin.ranges  
ByteBuffer 
kotlin.ranges  	ByteOrder 
kotlin.ranges  Canvas 
kotlin.ranges  CheckDocumentResults 
kotlin.ranges  
CheckInput 
kotlin.ranges  ChequeProcessorSDK 
kotlin.ranges  ChequeUtils 
kotlin.ranges  ClockwiseComparator 
kotlin.ranges  Collections 
kotlin.ranges  Color 
kotlin.ranges  
Comparator 
kotlin.ranges  Core 
kotlin.ranges  CountDownLatch 
kotlin.ranges  CvType 
kotlin.ranges  DEFAULT_MODEL_PATH 
kotlin.ranges  DataType 
kotlin.ranges  Date 
kotlin.ranges  	Exception 
kotlin.ranges  	Executors 
kotlin.ranges  File 
kotlin.ranges  FileChannel 
kotlin.ranges  FileInputStream 
kotlin.ranges  FileOutputStream 
kotlin.ranges  
FloatArray 
kotlin.ranges  FloatBuffer 
kotlin.ranges  HashMap 
kotlin.ranges  
INPUT_SIZE 
kotlin.ranges  IllegalStateException 
kotlin.ranges  ImageFormat 
kotlin.ranges  ImageOutputFormat 
kotlin.ranges  Imgproc 
kotlin.ranges  IncompatibleClassChangeError 
kotlin.ranges  
InputImage 
kotlin.ranges  Int 
kotlin.ranges  IntArray 
kotlin.ranges  IntRange 
kotlin.ranges  Interpreter 
kotlin.ranges  JvmField 
kotlin.ranges  	JvmStatic 
kotlin.ranges  KEY_CVN_CODE 
kotlin.ranges  KEY_CVN_RECT 
kotlin.ranges  
KEY_CVN_SCORE 
kotlin.ranges  KEY_ERROR_MESSAGE 
kotlin.ranges  KEY_HAS_SIGNATURE 
kotlin.ranges  KEY_HAS_VALID_CHEQUE_CONTOUR 
kotlin.ranges  KEY_IS_GENUINE 
kotlin.ranges  KEY_IS_SUCCESSFUL 
kotlin.ranges  
KEY_MICR_CODE 
kotlin.ranges  
KEY_MICR_RECT 
kotlin.ranges  KEY_MICR_SCORE 
kotlin.ranges  KEY_ORIGINAL_IMAGE 
kotlin.ranges  KEY_PROCESSED_IMAGE 
kotlin.ranges  KEY_SIGNATURE_RECT 
kotlin.ranges  KEY_SIGNATURE_SCORE 
kotlin.ranges  KEY_VALID_MICR_POSITION 
kotlin.ranges  Locale 
kotlin.ranges  Log 
kotlin.ranges  Mat 
kotlin.ranges  MatOfInt 
kotlin.ranges  
MatOfPoint 
kotlin.ranges  MatOfPoint2f 
kotlin.ranges  Math 
kotlin.ranges  Matrix 
kotlin.ranges  MemoryImage 
kotlin.ranges  ModelV2 
kotlin.ranges  OnnxModelHandler 
kotlin.ranges  
OnnxTensor 
kotlin.ranges  OpenCVLoader 
kotlin.ranges  
OpenCVRect 
kotlin.ranges  OrtEnvironment 
kotlin.ranges  OutOfMemoryError 
kotlin.ranges  OutputFormat 
kotlin.ranges  Paint 
kotlin.ranges  Pair 
kotlin.ranges  Point 
kotlin.ranges  ProcessedImage 
kotlin.ranges  
ProcessorLogs 
kotlin.ranges  Random 
kotlin.ranges  Rect 
kotlin.ranges  Result 
kotlin.ranges  RuntimeException 
kotlin.ranges  SEGMENTATION_TIMEOUT_SECONDS 
kotlin.ranges  Scalar 
kotlin.ranges  SimpleDateFormat 
kotlin.ranges  Size 
kotlin.ranges  String 
kotlin.ranges  
StringBuilder 
kotlin.ranges  SubjectSegmentation 
kotlin.ranges  SubjectSegmenterOptions 
kotlin.ranges  System 
kotlin.ranges  TAG 
kotlin.ranges  
TFLiteHandler 
kotlin.ranges  Tasks 
kotlin.ranges  TensorBuffer 
kotlin.ranges  TextRecognition 
kotlin.ranges  TextRecognizerOptions 
kotlin.ranges  TimeUnit 
kotlin.ranges  Triple 
kotlin.ranges  Utils 
kotlin.ranges  VerboseDetails 
kotlin.ranges  YOLOModelHandler 
kotlin.ranges  also 
kotlin.ranges  android 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  atan2 
kotlin.ranges  average 
kotlin.ranges  class_names 
kotlin.ranges  coerceIn 
kotlin.ranges  	compareBy 
kotlin.ranges  
component1 
kotlin.ranges  
component2 
kotlin.ranges  	divAssign 
kotlin.ranges  
doubleArrayOf 
kotlin.ranges  	emptyList 
kotlin.ranges  filter 
kotlin.ranges  floatArrayOf 
kotlin.ranges  forEach 
kotlin.ranges  forEachIndexed 
kotlin.ranges  format 
kotlin.ranges  	getOrNull 
kotlin.ranges  indices 
kotlin.ranges  instance 
kotlin.ranges  
intArrayOf 
kotlin.ranges  isDigit 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  java 
kotlin.ranges  	javaClass 
kotlin.ranges  kotlin 
kotlin.ranges  last 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  longArrayOf 
kotlin.ranges  map 
kotlin.ranges  mapOf 
kotlin.ranges  max 
kotlin.ranges  maxOf 
kotlin.ranges  	maxOrNull 
kotlin.ranges  minOf 
kotlin.ranges  	minOrNull 
kotlin.ranges  
mutableListOf 
kotlin.ranges  mutableMapOf 
kotlin.ranges  
plusAssign 
kotlin.ranges  pow 
kotlin.ranges  println 
kotlin.ranges  result 
kotlin.ranges  run 
kotlin.ranges  set 
kotlin.ranges  sortedBy 
kotlin.ranges  sortedByDescending 
kotlin.ranges  
sortedWith 
kotlin.ranges  sqrt 
kotlin.ranges  stackTraceToString 
kotlin.ranges  	substring 
kotlin.ranges  take 
kotlin.ranges  thenBy 
kotlin.ranges  to 
kotlin.ranges  toTypedArray 
kotlin.ranges  
trimIndent 
kotlin.ranges  until 
kotlin.ranges  use 
kotlin.ranges  	withIndex 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  A2iABoolean kotlin.sequences  AddressLineItem kotlin.sequences  Array kotlin.sequences  	ArrayList kotlin.sequences  AtomicReference kotlin.sequences  Bitmap kotlin.sequences  
BitmapFactory kotlin.sequences  	ByteArray kotlin.sequences  ByteArrayOutputStream kotlin.sequences  
ByteBuffer kotlin.sequences  	ByteOrder kotlin.sequences  Canvas kotlin.sequences  CheckDocumentResults kotlin.sequences  
CheckInput kotlin.sequences  ChequeProcessorSDK kotlin.sequences  ChequeUtils kotlin.sequences  ClockwiseComparator kotlin.sequences  Collections kotlin.sequences  Color kotlin.sequences  
Comparator kotlin.sequences  Core kotlin.sequences  CountDownLatch kotlin.sequences  CvType kotlin.sequences  DEFAULT_MODEL_PATH kotlin.sequences  DataType kotlin.sequences  Date kotlin.sequences  	Exception kotlin.sequences  	Executors kotlin.sequences  File kotlin.sequences  FileChannel kotlin.sequences  FileInputStream kotlin.sequences  FileOutputStream kotlin.sequences  
FloatArray kotlin.sequences  FloatBuffer kotlin.sequences  HashMap kotlin.sequences  
INPUT_SIZE kotlin.sequences  IllegalStateException kotlin.sequences  ImageFormat kotlin.sequences  ImageOutputFormat kotlin.sequences  Imgproc kotlin.sequences  IncompatibleClassChangeError kotlin.sequences  
InputImage kotlin.sequences  Int kotlin.sequences  IntArray kotlin.sequences  Interpreter kotlin.sequences  JvmField kotlin.sequences  	JvmStatic kotlin.sequences  KEY_CVN_CODE kotlin.sequences  KEY_CVN_RECT kotlin.sequences  
KEY_CVN_SCORE kotlin.sequences  KEY_ERROR_MESSAGE kotlin.sequences  KEY_HAS_SIGNATURE kotlin.sequences  KEY_HAS_VALID_CHEQUE_CONTOUR kotlin.sequences  KEY_IS_GENUINE kotlin.sequences  KEY_IS_SUCCESSFUL kotlin.sequences  
KEY_MICR_CODE kotlin.sequences  
KEY_MICR_RECT kotlin.sequences  KEY_MICR_SCORE kotlin.sequences  KEY_ORIGINAL_IMAGE kotlin.sequences  KEY_PROCESSED_IMAGE kotlin.sequences  KEY_SIGNATURE_RECT kotlin.sequences  KEY_SIGNATURE_SCORE kotlin.sequences  KEY_VALID_MICR_POSITION kotlin.sequences  Locale kotlin.sequences  Log kotlin.sequences  Mat kotlin.sequences  MatOfInt kotlin.sequences  
MatOfPoint kotlin.sequences  MatOfPoint2f kotlin.sequences  Math kotlin.sequences  Matrix kotlin.sequences  MemoryImage kotlin.sequences  ModelV2 kotlin.sequences  OnnxModelHandler kotlin.sequences  
OnnxTensor kotlin.sequences  OpenCVLoader kotlin.sequences  
OpenCVRect kotlin.sequences  OrtEnvironment kotlin.sequences  OutOfMemoryError kotlin.sequences  OutputFormat kotlin.sequences  Paint kotlin.sequences  Pair kotlin.sequences  Point kotlin.sequences  ProcessedImage kotlin.sequences  
ProcessorLogs kotlin.sequences  Random kotlin.sequences  Rect kotlin.sequences  Result kotlin.sequences  RuntimeException kotlin.sequences  SEGMENTATION_TIMEOUT_SECONDS kotlin.sequences  Scalar kotlin.sequences  SimpleDateFormat kotlin.sequences  Size kotlin.sequences  String kotlin.sequences  
StringBuilder kotlin.sequences  SubjectSegmentation kotlin.sequences  SubjectSegmenterOptions kotlin.sequences  System kotlin.sequences  TAG kotlin.sequences  
TFLiteHandler kotlin.sequences  Tasks kotlin.sequences  TensorBuffer kotlin.sequences  TextRecognition kotlin.sequences  TextRecognizerOptions kotlin.sequences  TimeUnit kotlin.sequences  Triple kotlin.sequences  Utils kotlin.sequences  VerboseDetails kotlin.sequences  YOLOModelHandler kotlin.sequences  also kotlin.sequences  android kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  atan2 kotlin.sequences  average kotlin.sequences  class_names kotlin.sequences  coerceIn kotlin.sequences  	compareBy kotlin.sequences  
component1 kotlin.sequences  
component2 kotlin.sequences  	divAssign kotlin.sequences  
doubleArrayOf kotlin.sequences  	emptyList kotlin.sequences  filter kotlin.sequences  floatArrayOf kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  format kotlin.sequences  	getOrNull kotlin.sequences  indices kotlin.sequences  instance kotlin.sequences  
intArrayOf kotlin.sequences  isDigit kotlin.sequences  
isNotEmpty kotlin.sequences  java kotlin.sequences  	javaClass kotlin.sequences  kotlin kotlin.sequences  last kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  longArrayOf kotlin.sequences  map kotlin.sequences  mapOf kotlin.sequences  max kotlin.sequences  maxOf kotlin.sequences  	maxOrNull kotlin.sequences  minOf kotlin.sequences  	minOrNull kotlin.sequences  
mutableListOf kotlin.sequences  mutableMapOf kotlin.sequences  
plusAssign kotlin.sequences  pow kotlin.sequences  println kotlin.sequences  result kotlin.sequences  run kotlin.sequences  set kotlin.sequences  sortedBy kotlin.sequences  sortedByDescending kotlin.sequences  
sortedWith kotlin.sequences  sqrt kotlin.sequences  stackTraceToString kotlin.sequences  	substring kotlin.sequences  take kotlin.sequences  thenBy kotlin.sequences  to kotlin.sequences  toTypedArray kotlin.sequences  
trimIndent kotlin.sequences  until kotlin.sequences  use kotlin.sequences  	withIndex kotlin.sequences  A2iABoolean kotlin.text  AddressLineItem kotlin.text  Array kotlin.text  	ArrayList kotlin.text  AtomicReference kotlin.text  Bitmap kotlin.text  
BitmapFactory kotlin.text  	ByteArray kotlin.text  ByteArrayOutputStream kotlin.text  
ByteBuffer kotlin.text  	ByteOrder kotlin.text  Canvas kotlin.text  CheckDocumentResults kotlin.text  
CheckInput kotlin.text  ChequeProcessorSDK kotlin.text  ChequeUtils kotlin.text  ClockwiseComparator kotlin.text  Collections kotlin.text  Color kotlin.text  
Comparator kotlin.text  Core kotlin.text  CountDownLatch kotlin.text  CvType kotlin.text  DEFAULT_MODEL_PATH kotlin.text  DataType kotlin.text  Date kotlin.text  	Exception kotlin.text  	Executors kotlin.text  File kotlin.text  FileChannel kotlin.text  FileInputStream kotlin.text  FileOutputStream kotlin.text  
FloatArray kotlin.text  FloatBuffer kotlin.text  HashMap kotlin.text  
INPUT_SIZE kotlin.text  IllegalStateException kotlin.text  ImageFormat kotlin.text  ImageOutputFormat kotlin.text  Imgproc kotlin.text  IncompatibleClassChangeError kotlin.text  
InputImage kotlin.text  Int kotlin.text  IntArray kotlin.text  Interpreter kotlin.text  JvmField kotlin.text  	JvmStatic kotlin.text  KEY_CVN_CODE kotlin.text  KEY_CVN_RECT kotlin.text  
KEY_CVN_SCORE kotlin.text  KEY_ERROR_MESSAGE kotlin.text  KEY_HAS_SIGNATURE kotlin.text  KEY_HAS_VALID_CHEQUE_CONTOUR kotlin.text  KEY_IS_GENUINE kotlin.text  KEY_IS_SUCCESSFUL kotlin.text  
KEY_MICR_CODE kotlin.text  
KEY_MICR_RECT kotlin.text  KEY_MICR_SCORE kotlin.text  KEY_ORIGINAL_IMAGE kotlin.text  KEY_PROCESSED_IMAGE kotlin.text  KEY_SIGNATURE_RECT kotlin.text  KEY_SIGNATURE_SCORE kotlin.text  KEY_VALID_MICR_POSITION kotlin.text  Locale kotlin.text  Log kotlin.text  Mat kotlin.text  MatOfInt kotlin.text  
MatOfPoint kotlin.text  MatOfPoint2f kotlin.text  Math kotlin.text  Matrix kotlin.text  MemoryImage kotlin.text  ModelV2 kotlin.text  OnnxModelHandler kotlin.text  
OnnxTensor kotlin.text  OpenCVLoader kotlin.text  
OpenCVRect kotlin.text  OrtEnvironment kotlin.text  OutOfMemoryError kotlin.text  OutputFormat kotlin.text  Paint kotlin.text  Pair kotlin.text  Point kotlin.text  ProcessedImage kotlin.text  
ProcessorLogs kotlin.text  Random kotlin.text  Rect kotlin.text  Result kotlin.text  RuntimeException kotlin.text  SEGMENTATION_TIMEOUT_SECONDS kotlin.text  Scalar kotlin.text  SimpleDateFormat kotlin.text  Size kotlin.text  String kotlin.text  
StringBuilder kotlin.text  SubjectSegmentation kotlin.text  SubjectSegmenterOptions kotlin.text  System kotlin.text  TAG kotlin.text  
TFLiteHandler kotlin.text  Tasks kotlin.text  TensorBuffer kotlin.text  TextRecognition kotlin.text  TextRecognizerOptions kotlin.text  TimeUnit kotlin.text  Triple kotlin.text  Utils kotlin.text  VerboseDetails kotlin.text  YOLOModelHandler kotlin.text  also kotlin.text  android kotlin.text  apply kotlin.text  arrayOf kotlin.text  atan2 kotlin.text  average kotlin.text  class_names kotlin.text  coerceIn kotlin.text  	compareBy kotlin.text  
component1 kotlin.text  
component2 kotlin.text  	divAssign kotlin.text  
doubleArrayOf kotlin.text  	emptyList kotlin.text  filter kotlin.text  floatArrayOf kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  format kotlin.text  	getOrNull kotlin.text  indices kotlin.text  instance kotlin.text  
intArrayOf kotlin.text  isDigit kotlin.text  
isNotEmpty kotlin.text  java kotlin.text  	javaClass kotlin.text  kotlin kotlin.text  last kotlin.text  let kotlin.text  listOf kotlin.text  longArrayOf kotlin.text  map kotlin.text  mapOf kotlin.text  max kotlin.text  maxOf kotlin.text  	maxOrNull kotlin.text  minOf kotlin.text  	minOrNull kotlin.text  
mutableListOf kotlin.text  mutableMapOf kotlin.text  
plusAssign kotlin.text  pow kotlin.text  println kotlin.text  result kotlin.text  run kotlin.text  set kotlin.text  sortedBy kotlin.text  sortedByDescending kotlin.text  
sortedWith kotlin.text  sqrt kotlin.text  stackTraceToString kotlin.text  	substring kotlin.text  take kotlin.text  thenBy kotlin.text  to kotlin.text  toTypedArray kotlin.text  
trimIndent kotlin.text  until kotlin.text  use kotlin.text  	withIndex kotlin.text  OpenCVLoader org.opencv.android  Utils org.opencv.android  	initDebug org.opencv.android.OpenCVLoader  bitmapToMat org.opencv.android.Utils  matToBitmap org.opencv.android.Utils  Core org.opencv.core  CvType org.opencv.core  Mat org.opencv.core  MatOfInt org.opencv.core  
MatOfPoint org.opencv.core  MatOfPoint2f org.opencv.core  Point org.opencv.core  Rect org.opencv.core  RotatedRect org.opencv.core  Scalar org.opencv.core  Size org.opencv.core  BORDER_CONSTANT org.opencv.core.Core  MinMaxLocResult org.opencv.core.Core  NORM_MINMAX org.opencv.core.Core  convertScaleAbs org.opencv.core.Core  copyMakeBorder org.opencv.core.Core  countNonZero org.opencv.core.Core  	minMaxLoc org.opencv.core.Core  	normalize org.opencv.core.Core  maxVal $org.opencv.core.Core.MinMaxLocResult  minVal $org.opencv.core.Core.MinMaxLocResult  CV_32F org.opencv.core.CvType  CV_32S org.opencv.core.CvType  CV_32SC2 org.opencv.core.CvType  CV_8U org.opencv.core.CvType  also org.opencv.core.Mat  channels org.opencv.core.Mat  clone org.opencv.core.Mat  cols org.opencv.core.Mat  	convertTo org.opencv.core.Mat  copyTo org.opencv.core.Mat  create org.opencv.core.Mat  empty org.opencv.core.Mat  equals org.opencv.core.Mat  	fromArray org.opencv.core.Mat  get org.opencv.core.Mat  getALSO org.opencv.core.Mat  getAlso org.opencv.core.Mat  height org.opencv.core.Mat  ones org.opencv.core.Mat  put org.opencv.core.Mat  release org.opencv.core.Mat  rows org.opencv.core.Mat  setTo org.opencv.core.Mat  size org.opencv.core.Mat  submat org.opencv.core.Mat  toList org.opencv.core.Mat  total org.opencv.core.Mat  type org.opencv.core.Mat  width org.opencv.core.Mat  get org.opencv.core.MatOfInt  size org.opencv.core.MatOfInt  	convertTo org.opencv.core.MatOfPoint  create org.opencv.core.MatOfPoint  empty org.opencv.core.MatOfPoint  equals org.opencv.core.MatOfPoint  put org.opencv.core.MatOfPoint  release org.opencv.core.MatOfPoint  rows org.opencv.core.MatOfPoint  toList org.opencv.core.MatOfPoint  	convertTo org.opencv.core.MatOfPoint2f  	fromArray org.opencv.core.MatOfPoint2f  get org.opencv.core.MatOfPoint2f  release org.opencv.core.MatOfPoint2f  x org.opencv.core.Point  y org.opencv.core.Point  height org.opencv.core.Rect  width org.opencv.core.Rect  x org.opencv.core.Rect  y org.opencv.core.Rect  points org.opencv.core.RotatedRect  height org.opencv.core.Size  	Imgcodecs org.opencv.imgcodecs  Imgproc org.opencv.imgproc  ADAPTIVE_THRESH_GAUSSIAN_C org.opencv.imgproc.Imgproc  CHAIN_APPROX_NONE org.opencv.imgproc.Imgproc  CHAIN_APPROX_SIMPLE org.opencv.imgproc.Imgproc  COLOR_BGR2GRAY org.opencv.imgproc.Imgproc  
COLOR_BGR2RGB org.opencv.imgproc.Imgproc  COLOR_BGR2RGBA org.opencv.imgproc.Imgproc  COLOR_GRAY2BGR org.opencv.imgproc.Imgproc  COLOR_RGBA2BGR org.opencv.imgproc.Imgproc  Canny org.opencv.imgproc.Imgproc  GaussianBlur org.opencv.imgproc.Imgproc  MORPH_BLACKHAT org.opencv.imgproc.Imgproc  MORPH_CLOSE org.opencv.imgproc.Imgproc  
MORPH_OPEN org.opencv.imgproc.Imgproc  
MORPH_RECT org.opencv.imgproc.Imgproc  
RETR_EXTERNAL org.opencv.imgproc.Imgproc  Sobel org.opencv.imgproc.Imgproc  
THRESH_BINARY org.opencv.imgproc.Imgproc  THRESH_BINARY_INV org.opencv.imgproc.Imgproc  THRESH_OTSU org.opencv.imgproc.Imgproc  adaptiveThreshold org.opencv.imgproc.Imgproc  approxPolyDP org.opencv.imgproc.Imgproc  	arcLength org.opencv.imgproc.Imgproc  boundingRect org.opencv.imgproc.Imgproc  contourArea org.opencv.imgproc.Imgproc  
convexHull org.opencv.imgproc.Imgproc  cvtColor org.opencv.imgproc.Imgproc  dilate org.opencv.imgproc.Imgproc  findContours org.opencv.imgproc.Imgproc  getPerspectiveTransform org.opencv.imgproc.Imgproc  getStructuringElement org.opencv.imgproc.Imgproc  minAreaRect org.opencv.imgproc.Imgproc  morphologyEx org.opencv.imgproc.Imgproc  resize org.opencv.imgproc.Imgproc  	threshold org.opencv.imgproc.Imgproc  warpPerspective org.opencv.imgproc.Imgproc  DataType org.tensorflow.lite  Interpreter org.tensorflow.lite  FLOAT32 org.tensorflow.lite.DataType  close org.tensorflow.lite.Interpreter  equals org.tensorflow.lite.Interpreter  runForMultipleInputsOutputs org.tensorflow.lite.Interpreter  close #org.tensorflow.lite.InterpreterImpl  runForMultipleInputsOutputs #org.tensorflow.lite.InterpreterImpl  TensorBuffer (org.tensorflow.lite.support.tensorbuffer  createFixedSize 5org.tensorflow.lite.support.tensorbuffer.TensorBuffer  
floatArray 5org.tensorflow.lite.support.tensorbuffer.TensorBuffer  
getFLOATArray 5org.tensorflow.lite.support.tensorbuffer.TensorBuffer  
getFloatArray 5org.tensorflow.lite.support.tensorbuffer.TensorBuffer  
loadBuffer 5org.tensorflow.lite.support.tensorbuffer.TensorBuffer  
setFloatArray 5org.tensorflow.lite.support.tensorbuffer.TensorBuffer  Output 
com.a2ia.data  ProcessedImageOutput 
com.a2ia.data  Status 
com.a2ia.data  convertImage com.a2ia.data.ImageConverter  PNG com.a2ia.data.ImageFormat  Any com.a2ia.data.Output  ProcessedImageOutput com.a2ia.data.Output  Status com.a2ia.data.Output  	ByteArray "com.a2ia.data.ProcessedImageOutput  ImageConverter "com.a2ia.data.ProcessedImageOutput  ImageFormat "com.a2ia.data.ProcessedImageOutput  OutputFormat "com.a2ia.data.ProcessedImageOutput  buffer "com.a2ia.data.ProcessedImageOutput  imageFormat "com.a2ia.data.ProcessedImageOutput  OK com.a2ia.data.Status  ImageConverter 	java.lang  ProcessedImageOutput 	java.lang  Status 	java.lang  ImageConverter kotlin  ProcessedImageOutput kotlin  Status kotlin  ImageConverter kotlin.annotation  ProcessedImageOutput kotlin.annotation  Status kotlin.annotation  ImageConverter kotlin.collections  ProcessedImageOutput kotlin.collections  Status kotlin.collections  ImageConverter kotlin.comparisons  ProcessedImageOutput kotlin.comparisons  Status kotlin.comparisons  ImageConverter 	kotlin.io  ProcessedImageOutput 	kotlin.io  Status 	kotlin.io  ImageConverter 
kotlin.jvm  ProcessedImageOutput 
kotlin.jvm  Status 
kotlin.jvm  ImageConverter 
kotlin.ranges  ProcessedImageOutput 
kotlin.ranges  Status 
kotlin.ranges  ImageConverter kotlin.sequences  ProcessedImageOutput kotlin.sequences  Status kotlin.sequences  ImageConverter kotlin.text  ProcessedImageOutput kotlin.text  Status kotlin.text  PreprocessedImage com.a2ia.data.Output  PreprocessedImage 
com.a2ia.data  preprocessedImage com.a2ia.data.Output  BaseImageOutput 
com.a2ia.data  locatedDocumentImage com.a2ia.data.Output  LocatedDocumentImage 
com.a2ia.data  convertedOriginalImage com.a2ia.data.Output  ConvertedOriginalImage 
com.a2ia.data  Yes com.a2ia.data.A2iABoolean  equals com.a2ia.data.A2iABoolean  Boolean com.a2ia.data.BaseImageOutput  	ByteArray com.a2ia.data.BaseImageOutput  ImageConverter com.a2ia.data.BaseImageOutput  ImageFormat com.a2ia.data.BaseImageOutput  OutputFormat com.a2ia.data.BaseImageOutput  buffer com.a2ia.data.BaseImageOutput  imageFormat com.a2ia.data.BaseImageOutput  setImageData com.a2ia.data.BaseImageOutput  setOriginalImageData com.a2ia.data.BaseImageOutput  setProcessedImageData com.a2ia.data.BaseImageOutput  	ByteArray $com.a2ia.data.ConvertedOriginalImage  OutputFormat $com.a2ia.data.ConvertedOriginalImage  setImageData $com.a2ia.data.ConvertedOriginalImage  setOriginalImageData $com.a2ia.data.ConvertedOriginalImage  	ByteArray "com.a2ia.data.LocatedDocumentImage  OutputFormat "com.a2ia.data.LocatedDocumentImage  setImageData "com.a2ia.data.LocatedDocumentImage  setProcessedImageData "com.a2ia.data.LocatedDocumentImage  A2iABoolean com.a2ia.data.Output  	ByteArray com.a2ia.data.Output  ConvertedOriginalImage com.a2ia.data.Output  Input com.a2ia.data.Output  LocatedDocumentImage com.a2ia.data.Output  	ByteArray com.a2ia.data.PreprocessedImage  OutputFormat com.a2ia.data.PreprocessedImage  setImageData com.a2ia.data.PreprocessedImage  setProcessedImageData com.a2ia.data.PreprocessedImage  ConvertedOriginalImage 	java.lang  LocatedDocumentImage 	java.lang  PreprocessedImage 	java.lang  ConvertedOriginalImage kotlin  LocatedDocumentImage kotlin  PreprocessedImage kotlin  ConvertedOriginalImage kotlin.annotation  LocatedDocumentImage kotlin.annotation  PreprocessedImage kotlin.annotation  ConvertedOriginalImage kotlin.collections  LocatedDocumentImage kotlin.collections  PreprocessedImage kotlin.collections  ConvertedOriginalImage kotlin.comparisons  LocatedDocumentImage kotlin.comparisons  PreprocessedImage kotlin.comparisons  ConvertedOriginalImage 	kotlin.io  LocatedDocumentImage 	kotlin.io  PreprocessedImage 	kotlin.io  ConvertedOriginalImage 
kotlin.jvm  LocatedDocumentImage 
kotlin.jvm  PreprocessedImage 
kotlin.jvm  ConvertedOriginalImage 
kotlin.ranges  LocatedDocumentImage 
kotlin.ranges  PreprocessedImage 
kotlin.ranges  ConvertedOriginalImage kotlin.sequences  LocatedDocumentImage kotlin.sequences  PreprocessedImage kotlin.sequences  ConvertedOriginalImage kotlin.text  LocatedDocumentImage kotlin.text  PreprocessedImage kotlin.text  ImageOutputType 
com.a2ia.data  getLET android.graphics.Bitmap  getLet android.graphics.Bitmap  let android.graphics.Bitmap  buffer $com.a2ia.data.ConvertedOriginalImage  CONVERTED_ORIGINAL com.a2ia.data.ImageOutputType  LOCATED_DOCUMENT com.a2ia.data.ImageOutputType  PREPROCESSED com.a2ia.data.ImageOutputType  buffer "com.a2ia.data.LocatedDocumentImage  	Exception com.a2ia.data.Output  ImageOutputType com.a2ia.data.Output  String com.a2ia.data.Output  android com.a2ia.data.Output  extractProcessedImageData com.a2ia.data.Output  
getANDROID com.a2ia.data.Output  
getAndroid com.a2ia.data.Output  getJAVA com.a2ia.data.Output  getJava com.a2ia.data.Output  getLET com.a2ia.data.Output  getLet com.a2ia.data.Output  java com.a2ia.data.Output  	javaClass com.a2ia.data.Output  let com.a2ia.data.Output  buffer com.a2ia.data.PreprocessedImage  Class 	java.lang  ImageOutputType 	java.lang  	getMethod java.lang.Class  Method java.lang.reflect  invoke "java.lang.reflect.AccessibleObject  invoke java.lang.reflect.Executable  invoke java.lang.reflect.Method  ImageOutputType kotlin  getJAVAClass 
kotlin.Any  getJavaClass 
kotlin.Any  	Companion 
kotlin.String  ImageOutputType kotlin.annotation  ImageOutputType kotlin.collections  ImageOutputType kotlin.comparisons  ImageOutputType 	kotlin.io  ImageOutputType 
kotlin.jvm  ImageOutputType 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  ImageOutputType kotlin.sequences  ImageOutputType kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  