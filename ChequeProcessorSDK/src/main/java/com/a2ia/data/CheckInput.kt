package com.a2ia.data

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import java.io.File
import java.io.FileInputStream
import java.io.InputStream
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * A2iA支票处理输入端口
 * 负责处理图像输入并调用SDK完成支票处理流程
 */
class CheckInput private constructor(private val context: Context) {
    companion object {
        private const val TAG = "A2iACheckInput"
        private var instance: CheckInput? = null

        @JvmStatic
        fun getInstance(context: Context): CheckInput {
            if (instance == null) {
                instance = CheckInput(context.applicationContext)
            }
            return instance!!
        }
    }

    // 处理线程池
    private val executorService: ExecutorService = Executors.newSingleThreadExecutor()

    /**
     * 处理图像文件
     * @param imagePath 图像文件路径
     * @return 处理结果
     */
    fun processImage(imagePath: String): CheckDocumentResults {
        val results = CheckDocumentResults()

        try {
            // 将图像文件转换为Bitmap
            val file = File(imagePath)
            if (!file.exists()) {
                results.setIsSuccessful(false)
                results.setErrorMessage("图像文件不存在: $imagePath")
                return results
            }

            val bitmap = loadBitmapFromFile(imagePath)
            if (bitmap == null) {
                results.setIsSuccessful(false)
                results.setErrorMessage("无法加载图像文件: $imagePath")
                return results
            }

            return processImageBitmap(bitmap)
        } catch (e: Exception) {
            Log.e(TAG, "处理图像文件失败: ${e.message}", e)
            results.setIsSuccessful(false)
            results.setErrorMessage("处理图像文件失败: ${e.message}")
            return results
        }
    }

    /**
     * 处理输入流中的图像
     * @param inputStream 图像输入流
     * @return 处理结果
     */
    fun processImage(inputStream: InputStream): CheckDocumentResults {
        val results = CheckDocumentResults()

        try {
            // 将输入流转换为Bitmap
            val bitmap = BitmapFactory.decodeStream(inputStream)
            if (bitmap == null) {
                results.setIsSuccessful(false)
                results.setErrorMessage("无法从输入流加载图像")
                return results
            }

            return processImageBitmap(bitmap)
        } catch (e: Exception) {
            Log.e(TAG, "处理输入流图像失败: ${e.message}", e)
            results.setIsSuccessful(false)
            results.setErrorMessage("处理输入流图像失败: ${e.message}")
            return results
        } finally {
            try {
                inputStream.close()
            } catch (e: Exception) {
                Log.e(TAG, "关闭输入流失败", e)
            }
        }
    }

    /**
     * 处理Bitmap图像
     * @param bitmap 图像Bitmap
     * @return 处理结果
     */
    fun processImageBitmap(bitmap: Bitmap): CheckDocumentResults {
        val results = CheckDocumentResults()

        try {
            // 获取SDK实例
            val sdk = ChequeProcessorSDK.getInstance(context)
            if (sdk == null) {
                results.setIsSuccessful(false)
                results.setErrorMessage("无法初始化支票处理SDK")
                return results
            }

            // 保存原始图像
            results.setOriginalImage(bitmap)

            // 处理支票图像
            val processedBitmap = sdk.processChequeImage(bitmap)
            if (processedBitmap == null) {
                results.setIsSuccessful(false)
                results.setErrorMessage("支票图像处理失败")
                return results
            }

            // 保存处理后的图像
            results.setProcessedImage(processedBitmap)

            // 执行MICR码识别
            val (micrCode, micrScore) = sdk.recognizeMICRCode()
            results.setMicrCode(micrCode, micrScore)

            // 获取MICR区域
            results.setMicrRect(sdk.micrRegion)

            // 识别CVN码
            val (cvnCode, cvnScore) = sdk.recognizeCVNCode()
            results.setCvnCode(cvnCode, cvnScore)

            // 获取CVN区域
            results.setCvnRect(sdk.cvnRegion)

            // 检测签名
            val (hasSignature, signatureScore) = sdk.detectSignature()
            results.setHasSignature(hasSignature, signatureScore)
            // 获取签名区域
            results.setSignatureRect(sdk.getDetectedSignatureRegion())

            // 识别支票真伪
            val isGenuine = sdk.isGenuineCheque
            results.setIsGenuine(isGenuine)

            val hasValidChequeContour = sdk.getHasValidChequeContour()
            results.getHasValidChequeContour(hasValidChequeContour)
            val validMicrPosition = sdk.getValidMicrPosition()
            results.getValidMicrPosition(validMicrPosition)

            // 设置处理成功
            results.setIsSuccessful(true)

            return results
        } catch (e: Exception) {
            Log.e(TAG, "处理Bitmap图像失败: ${e.message}", e)
            results.setIsSuccessful(false)
            results.setErrorMessage("处理Bitmap图像失败: ${e.message}")
            return results
        }
    }

    /**
     * 异步处理图像
     * @param bitmap 图像Bitmap
     * @param callback 处理完成后的回调
     */
    fun processImageAsync(bitmap: Bitmap, callback: (CheckDocumentResults) -> Unit) {
        executorService.execute {
            val results = processImageBitmap(bitmap)
            callback(results)
        }
    }

    /**
     * 异步处理图像文件
     * @param imagePath 图像文件路径
     * @param callback 处理完成后的回调
     */
    fun processImageAsync(imagePath: String, callback: (CheckDocumentResults) -> Unit) {
        executorService.execute {
            val results = processImage(imagePath)
            callback(results)
        }
    }

    /**
     * 异步处理输入流中的图像
     * @param inputStream 图像输入流
     * @param callback 处理完成后的回调
     */
    fun processImageAsync(inputStream: InputStream, callback: (CheckDocumentResults) -> Unit) {
        executorService.execute {
            val results = processImage(inputStream)
            callback(results)
        }
    }

    /**
     * 从文件加载Bitmap
     * @param filePath 文件路径
     * @return 加载的Bitmap，失败返回null
     */
    private fun loadBitmapFromFile(filePath: String): Bitmap? {
        return try {
            val options = BitmapFactory.Options()
            options.inPreferredConfig = Bitmap.Config.ARGB_8888
            BitmapFactory.decodeFile(filePath, options)
        } catch (e: Exception) {
            Log.e(TAG, "加载图像文件失败: ${e.message}", e)
            null
        }
    }

    /**
     * 获取SDK实例
     * @return ChequeProcessorSDK实例
     */
    fun getSDK(): ChequeProcessorSDK {
        return ChequeProcessorSDK.getInstance(context)!!
    }

    /**
     * 释放资源
     */
    fun release() {
        executorService.shutdown()
        instance = null
    }
}