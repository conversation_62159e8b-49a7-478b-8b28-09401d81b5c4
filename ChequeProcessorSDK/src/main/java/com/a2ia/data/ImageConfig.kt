package com.a2ia.data

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import java.io.ByteArrayOutputStream

/**
 * 图像格式配置
 */
public enum class ImageFormat {
    JPEG,
    PNG;
}

/**
 * 输出格式配置
 */
public enum class OutputFormat {
    JPEG,
    PNG;

    public fun applyToPreprocessed(input: Input) {
        input.verboseDetails.preprocessedImageFormat.outputFormat = this
    }

    public fun applyToLocated(input: Input) {
        input.verboseDetails.locatedDocumentImageFormat.outputFormat = this
    }
}

/**
 * 图像转换工具类
 */
public object ImageConverter {
    private val reusableStream = ByteArrayOutputStream()

    public fun convertImage(imageData: ByteArray, format: OutputFormat, grayscale: Boolean = false): ByteArray {
        // 直接解码，不设置任何density
        val bitmap = BitmapFactory.decodeByteArray(imageData, 0, imageData.size)
        try {
            val processedBitmap = if (grayscale) {
                convertToGrayscale(bitmap)
            } else {
                bitmap
            }
            
            // 在压缩前设置density
            processedBitmap.density = 200
            
            reusableStream.reset()
            return when (format) {
                OutputFormat.JPEG -> convertToJPEG(processedBitmap)
                OutputFormat.PNG -> convertToPNG(processedBitmap)
            }
        } finally {
            if (!grayscale) {
                bitmap.recycle()
            }
        }
    }

    private fun convertToGrayscale(sourceBitmap: Bitmap): Bitmap {
        val grayscaleBitmap = Bitmap.createBitmap(sourceBitmap.width, sourceBitmap.height, Bitmap.Config.ARGB_8888)
        val canvas = android.graphics.Canvas(grayscaleBitmap)
        
        val paint = android.graphics.Paint().apply {
            // 降低对比度，增加亮度补偿来减少过曝
            val contrast = 1.8f  // 从2.5f降低到1.8f
            val brightness = -15f  // 从-40f调整到-15f，减少暗部压抑

            // 对比度矩阵
            val contrastMatrix = floatArrayOf(
                contrast, 0f, 0f, 0f, brightness,
                0f, contrast, 0f, 0f, brightness,
                0f, 0f, contrast, 0f, brightness,
                0f, 0f, 0f, 1f, 0f
            )

            // 调整灰度转换权重，更接近自然观感
            val grayMatrix = floatArrayOf(
                0.35f, 0.55f, 0.1f, 0f, 0f,  // 调整RGB权重，保持较强对比但更自然
                0.35f, 0.55f, 0.1f, 0f, 0f,
                0.35f, 0.55f, 0.1f, 0f, 0f,
                0f, 0f, 0f, 1f, 0f
            )

            // 组合矩阵效果
            val colorMatrix = android.graphics.ColorMatrix(grayMatrix)
            colorMatrix.postConcat(android.graphics.ColorMatrix(contrastMatrix))
            
            colorFilter = android.graphics.ColorMatrixColorFilter(colorMatrix)

            // 保持锐化效果但稍微柔和一点
            maskFilter = android.graphics.BlurMaskFilter(0.7f, android.graphics.BlurMaskFilter.Blur.NORMAL)
            isAntiAlias = true
            isDither = true
            isFilterBitmap = true
            
            strokeWidth = 0f
            style = android.graphics.Paint.Style.FILL
            flags = flags or android.graphics.Paint.FILTER_BITMAP_FLAG
        }

        canvas.drawBitmap(sourceBitmap, 0f, 0f, paint)
        return grayscaleBitmap
    }

    private fun convertToJPEG(bitmap: Bitmap): ByteArray {
        reusableStream.reset()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, reusableStream)
        bitmap.recycle()
        return reusableStream.toByteArray()
    }

    private fun convertToPNG(bitmap: Bitmap): ByteArray {
        reusableStream.reset()
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, reusableStream)
        bitmap.recycle()
        return reusableStream.toByteArray()
    }

    public fun clear() {
        reusableStream.reset()
    }
}

/**
 * A2iA SDK的布尔类型配置
 */
enum class A2iABoolean {
    Yes,
    No;

    fun applyToVerbose(input: Input) {
        input.verbose = this
    }

    fun applyToPreprocessedImage(input: Input) {
        input.verboseDetails.preprocessedImage = this
    }

    fun applyToLocatedImage(input: Input) {
        input.verboseDetails.locatedDocumentImage = this
    }
}

/**
 * 图像输出格式配置
 */
class ImageOutputFormat {
    var outputFormat: OutputFormat = OutputFormat.JPEG
}

/**
 * 详细信息配置
 */
class VerboseDetails {
    var preprocessedImage: A2iABoolean = A2iABoolean.No
    var preprocessedImageFormat: ImageOutputFormat = ImageOutputFormat()
    var locatedDocumentImage: A2iABoolean = A2iABoolean.No
    var locatedDocumentImageFormat: ImageOutputFormat = ImageOutputFormat()
}

/**
 * 内存图像配置
 */
class MemoryImage {
    var imageFormat: ImageFormat = ImageFormat.JPEG
    var buffer: ByteArray? = null
}

/**
 * 处理后的图像缓冲区
 */
class ProcessedImage {
    var imageFormat: ImageFormat = ImageFormat.JPEG
    var buffer: ByteArray? = null
    
    companion object {
        fun fromBitmap(bitmap: Bitmap): ProcessedImage {
            return ProcessedImage().apply {
                val stream = ByteArrayOutputStream()
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, stream)
                buffer = stream.toByteArray()
            }
        }
    }
}

/**
 * 输入配置类
 */
class Input {
    var image: MemoryImage = MemoryImage()
    var verbose: A2iABoolean = A2iABoolean.No
    var verboseDetails: VerboseDetails = VerboseDetails()
}

/**
 * 输出状态枚举
 */
enum class Status {
    OK, ERROR
}

/**
 * 基础图像输出类
 * 提供简洁的 buffer 访问方式
 */
abstract class BaseImageOutput {
    var buffer: ByteArray? = null
    var imageFormat: ImageFormat = ImageFormat.JPEG

    /**
     * 设置图像数据
     */
    protected fun setImageData(imageData: ByteArray?, format: OutputFormat, grayscale: Boolean = false) {
        if (imageData != null) {
            buffer = ImageConverter.convertImage(imageData, format, grayscale)
            imageFormat = when (format) {
                OutputFormat.JPEG -> ImageFormat.JPEG
                OutputFormat.PNG -> ImageFormat.PNG
            }
        }
    }
}

/**
 * 转换后的原始图像 - 基于原始图像
 */
class ConvertedOriginalImage : BaseImageOutput() {
    /**
     * 设置原始图像数据并转换格式
     */
    fun setOriginalImageData(imageData: ByteArray?, format: OutputFormat = OutputFormat.JPEG) {
        setImageData(imageData, format, grayscale = false)
    }
}

/**
 * 预处理图像 - 基于处理后图像的黑白版
 */
class PreprocessedImage : BaseImageOutput() {
    /**
     * 设置处理后图像数据并转换为黑白版
     */
    fun setProcessedImageData(imageData: ByteArray?, format: OutputFormat = OutputFormat.JPEG) {
        setImageData(imageData, format, grayscale = true)
    }
}

/**
 * 定位文档图像 - 基于处理后图像
 */
class LocatedDocumentImage : BaseImageOutput() {
    /**
     * 设置处理后图像数据
     */
    fun setProcessedImageData(imageData: ByteArray?, format: OutputFormat = OutputFormat.JPEG) {
        setImageData(imageData, format, grayscale = false)
    }
}

/**
 * SDK 输出类
 * 包含处理结果和三个具体的图像变量
 */
class Output {
    val status = Status.OK
    val statusContext = ""

    // 三个具体的图像变量
    val convertedOriginalImage = ConvertedOriginalImage()    // 原始图像为基底
    val preprocessedImage = PreprocessedImage()              // 处理后图像为基底的黑白版
    val locatedDocumentImage = LocatedDocumentImage()        // 处理后图像为基底

    val documentResults: Any? = null

    /**
     * 统一设置图像数据的方法 - 隐藏具体实现细节
     * @param originalImageData 原始图像数据
     * @param checkOutput 检查输出结果，从中提取处理后图像
     * @param imageConfig 图像配置
     */
    fun setImageData(originalImageData: ByteArray?, checkOutput: Any?, imageConfig: Input) {
        // 从 checkOutput 中提取处理后的图像数据（隐藏实现细节）
        val processedImageData = extractProcessedImageData(checkOutput)

        // 1. 设置 convertedOriginalImage - 原始图像为基底
        if (imageConfig.verboseDetails.preprocessedImage == A2iABoolean.Yes) {
            val format = imageConfig.verboseDetails.preprocessedImageFormat.outputFormat
            convertedOriginalImage.setOriginalImageData(originalImageData, format)
        }

        // 2. 设置 preprocessedImage - 处理后图像为基底的黑白版
        // 3. 设置 locatedDocumentImage - 处理后图像为基底
        if (imageConfig.verboseDetails.locatedDocumentImage == A2iABoolean.Yes) {
            val format = imageConfig.verboseDetails.locatedDocumentImageFormat.outputFormat

            // 设置黑白版本 (preprocessedImage)
            preprocessedImage.setProcessedImageData(processedImageData, format)

            // 设置彩色版本 (locatedDocumentImage)
            locatedDocumentImage.setProcessedImageData(processedImageData, format)
        }
    }

    /**
     * 从 checkOutput 中提取处理后图像数据 - 隐藏具体实现
     */
    private fun extractProcessedImageData(checkOutput: Any?): ByteArray? {
        return try {
            // 这里需要导入相关类，但为了隐藏实现细节，我们使用反射
            val getResultMethod = checkOutput?.javaClass?.getMethod("getResult", String::class.java)
            val bitmap = getResultMethod?.invoke(checkOutput, "KEY_PROCESSED_IMAGE") as? android.graphics.Bitmap

            bitmap?.let {
                val stream = java.io.ByteArrayOutputStream()
                it.compress(android.graphics.Bitmap.CompressFormat.JPEG, 100, stream)
                stream.toByteArray()
            }
        } catch (e: Exception) {
            null
        }
    }

}