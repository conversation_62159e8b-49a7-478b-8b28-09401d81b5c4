package com.a2ia.data

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Rect
import android.os.Build
import android.os.Environment
import android.util.Log
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.Tasks
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.segmentation.subject.SubjectSegmentation
import com.google.mlkit.vision.segmentation.subject.SubjectSegmenter
import com.google.mlkit.vision.segmentation.subject.SubjectSegmenterOptions
import com.google.mlkit.vision.segmentation.subject.SubjectSegmentationResult
import com.google.mlkit.vision.text.Text
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.TextRecognizer
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import org.opencv.android.OpenCVLoader
import org.opencv.android.Utils
import org.opencv.core.Core
import org.opencv.core.CvType
import org.opencv.core.Mat
import org.opencv.core.MatOfInt
import org.opencv.core.MatOfPoint
import org.opencv.core.MatOfPoint2f
import org.opencv.core.Point
import org.opencv.core.Rect as OpenCVRect
import org.opencv.core.RotatedRect
import org.opencv.core.Scalar
import org.opencv.core.Size
import org.opencv.imgproc.Imgproc
import org.opencv.imgcodecs.Imgcodecs
import org.tensorflow.lite.Interpreter
import java.io.*
import java.lang.Exception
import java.lang.Math
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.MappedByteBuffer
import java.nio.channels.FileChannel
import java.text.SimpleDateFormat
import java.util.*
import java.util.ArrayList
import java.util.Arrays
import java.util.Collections
import java.util.Comparator
import java.util.List
import java.util.concurrent.*
import java.util.concurrent.atomic.AtomicReference
import java.util.stream.Collectors
import kotlin.math.*

// 添加ProcessorLogs类定义
class ProcessorLogs {
    fun log(message: String) {
        Log.d("ProcessorLogs", message)
    }
}

/**
 * 支票处理SDK主类 - 基于V0.9版本完全实现
 * 提供支票图像处理、MICR码识别、签名检测等功能
 */
class ChequeProcessorSDK private constructor(val context: Context) {
    // 静态成员和方法
    companion object {
        private const val SEGMENTATION_TIMEOUT_SECONDS = 30
        private const val TAG = "ChequeProcessorSDK"
        private var instance: ChequeProcessorSDK? = null
        private var isProcessingCheque = false
        
        @JvmField
        val log = ProcessorLogs()
        
        @JvmField
        val micrCodes = HashMap<String, ArrayList<String>>()

        @JvmStatic
        fun init(context: Context, gzFile: String): Boolean {
            try {
                instance = ChequeProcessorSDK(context)
                return true
            } catch (e: Exception) {
                e.printStackTrace()
                return false
            }
        }

        @JvmStatic
        fun getInstance(): ChequeProcessorSDK? {
            return instance
        }

        @JvmStatic
        fun getInstance(context: Context): ChequeProcessorSDK? {
            if (instance == null) {
                init(context, "")
            }
            return instance
        }

        @JvmStatic
        fun isProcessing(): Boolean {
            return isProcessingCheque
        }

        @JvmStatic
        fun setProcessingCheque(processingCheque: Boolean) {
            isProcessingCheque = processingCheque
        }
    }

    // 实例成员和方法
    // MICR码列表 - 这是实例变量，不是静态变量
    val MICRCodes: MutableList<Mat> = ArrayList()

    // 存储图像处理结果
    private var processedMat: Mat? = null
    private var processedBitmap: Bitmap? = null
    private var originalBitmap: Bitmap? = null
    private var segmentedBitmap: Bitmap? = null
    
    private var onnxModelHandler: OnnxModelHandler? = null
    private var tfLiteHandler: TFLiteHandler? = null
    private var yoloModelHandler: YOLOModelHandler? = null

    // 识别结果的区域
    private var micrRect: Rect? = null
    private var cvnRect: Rect? = null
    private var signatureRect: Rect? = null

    // 存储检测结果
    private var hasSignature = false
    private var isGenuine = false
    private var micrCode: String? = null
    private var cvnCode: String? = null
    private var micrScore: Int = 0
    private var cvnScore: Int = 0
    private var hasValidChequeContour: Boolean = false
    private var validMicrPosition: Boolean = false

    private val lineBoundingBoxes: MutableList<Text.Line> = ArrayList()

    private var signatureScore: Int = 0  // 添加签名分数字段

    /**
     * 初始化构造函数
     */
    init {
        // 初始化OpenCV
        val isOpenCVInitialized = OpenCVLoader.initDebug()
        if (!isOpenCVInitialized) {
            Log.e(TAG, "无法初始化OpenCV")
        }

        // 初始化模型处理器
        try {
            onnxModelHandler = OnnxModelHandler(context)
            tfLiteHandler = TFLiteHandler(context)
            yoloModelHandler = YOLOModelHandler(context) { message ->
                Log.d(TAG, message)  // 使用ChequeProcessorSDK的TAG输出YOLO的日志
            }
        } catch (e: Exception) {
            Log.e(TAG, "初始化模型处理器失败: ${e.message}", e)
        }
    }

    /**
     * 处理支票图像 - SDK的主要入口方法
     * @param bitmap 支票图像
     * @return 处理后的图像，处理失败返回null
     */
    fun processChequeImage(bitmap: Bitmap): Bitmap? {
        try {
            Log.d(TAG, "开始处理支票图像")

            // 保存原始图像（进行缩放处理）
            val scaledBitmap = scaleBitmapIfNeeded(bitmap, 2048)
            this.originalBitmap = scaledBitmap

            // 清除之前的处理结果
            clearPreviousResults()

            if (scaledBitmap == null) {
                Log.e(TAG, "缩放图像失败")
                return null
            }

            // 使用MLKit进行支票分割
            val options: SubjectSegmenterOptions = SubjectSegmenterOptions.Builder()
                .enableForegroundBitmap()
                .build()

            val segmenter: SubjectSegmenter = SubjectSegmentation.getClient(options)
            val image: InputImage = InputImage.fromBitmap(scaledBitmap, 0)

            // 使用CountDownLatch进行同步处理
            val foregroundBitmapRef: AtomicReference<Bitmap> = AtomicReference()
            val latch: CountDownLatch = CountDownLatch(1)

            // 执行分割
            segmenter.process(image)
                .addOnSuccessListener { result ->
                    try {
                        val foregroundBitmap = result.foregroundBitmap
                        foregroundBitmapRef.set(foregroundBitmap)
                        segmentedBitmap = foregroundBitmap
                    } catch (e: Exception) {
                        Log.e(
                            TAG,
                            "处理分割结果时出错: ${e.message}",
                            e
                        )
                    } finally {
                        latch.countDown()
                    }
                }
                .addOnFailureListener { e ->
                    Log.e(
                        TAG,
                        "分割过程出错: ${e.message}",
                        e
                    )
                    latch.countDown()
                }

            // 等待分割完成
            val completed: Boolean = latch.await(SEGMENTATION_TIMEOUT_SECONDS.toLong(), TimeUnit.SECONDS)
            if (!completed) {
                Log.e(TAG, "分割超时，使用原始图像继续处理")
                // 超时时使用原图继续处理
                if (scaledBitmap != null) {
                    foregroundBitmapRef.set(scaledBitmap.copy(scaledBitmap.config ?: Bitmap.Config.ARGB_8888, true))
                }
            }

            var foregroundBitmap: Bitmap? = foregroundBitmapRef.get()
            if (foregroundBitmap == null) {
                Log.e(TAG, "未获取到前景图像，使用原始图像")
                // 如果分割失败，使用原图
                if (scaledBitmap != null) {
                    foregroundBitmap = scaledBitmap.copy(scaledBitmap.config ?: Bitmap.Config.ARGB_8888, true)
                } else {
                    return null // 如果无法获取图像则返回null
                }
            }

            // 这里已经确保foregroundBitmap不为null
            Log.d(TAG, "获取前景图像完成，开始Mat转换")

            // 将分割后的Bitmap转换为Mat
            val frame: Mat = ChequeUtils.convertBitmapToMat(foregroundBitmap!!)

            // 预处理
            val preProcessedMat: Mat = preProcessFrame(frame)
            frame.release() // 释放不再需要的Mat

            // 提取轮廓
            val contours: List<MatOfPoint> = extractContours(preProcessedMat)
            preProcessedMat.release() // 释放不再需要的Mat

            // 选择支票轮廓
            val selectedContours: List<MatOfPoint> = selectChequeContour(contours)

            // 释放所有轮廓
            for (contour in contours) {
                if (!contour.empty()) {
                    contour.release()
                }
            }

            if (selectedContours.isEmpty()) {
                Log.e(TAG, "未找到有效支票轮廓")
                return null
            }

            // 裁剪和透视变换
            val croppedMat: Mat = cropCnt(
                ChequeUtils.convertBitmapToMat(foregroundBitmap),
                selectedContours[0]
            )

            // 释放选中的轮廓
            for (contour in selectedContours) {
                if (contour != null) {
                    contour.release()
                }
            }

            // 回收分割后的位图，不再需要
            if (foregroundBitmap != null && foregroundBitmap !== scaledBitmap) {
                foregroundBitmap.recycle()
            }

            // 如果scaledBitmap不是原始输入的bitmap，也回收它
            if (scaledBitmap !== bitmap && scaledBitmap != null) {
                scaledBitmap.recycle()
            }

            // 保存处理结果
            this.processedMat = croppedMat
            val resultBitmap: Bitmap? = ChequeUtils.convertMatToBitmap(croppedMat)
            this.processedBitmap = resultBitmap

            // 执行文本识别
            if (resultBitmap != null) {
                processTextRecognition(resultBitmap)
            }

            return resultBitmap
        } catch (e: Exception) {
            Log.e(TAG, "处理支票图像失败: ${e.message}", e)
            return null
        }
    }

    fun getHasValidChequeContour(): Boolean {
        return hasValidChequeContour
    }

    fun getValidMicrPosition(): Boolean {
        return validMicrPosition
    }

    /**
     * 清除之前的处理结果
     */
    private fun clearPreviousResults() {
        // 清除存储的数据
        val localMat = processedMat
        if (localMat != null && !localMat.empty()) {
            localMat.release()
        }

        if (processedBitmap != null && !processedBitmap!!.isRecycled) {
            processedBitmap!!.recycle()
            processedBitmap = null
        }

        if (segmentedBitmap != null && !segmentedBitmap!!.isRecycled) {
            segmentedBitmap!!.recycle()
            segmentedBitmap = null
        }

        // 原始图像不回收，因为可能是从外部传入的
        originalBitmap = null

        // 重置识别结果
        micrRect = null
        cvnRect = null
        signatureRect = null
        hasSignature = false
        isGenuine = false
        micrCode = null
        cvnCode = null
        hasValidChequeContour = false
        validMicrPosition = false

        // 清除MICR码列表
        for (mat in MICRCodes) {
            if (mat != null && !mat.empty()) {
                mat.release()
            }
        }
        MICRCodes.clear()

        // 清除文本行列表
        lineBoundingBoxes.clear()

        // 强制GC
        System.gc()
    }

    /**
     * 处理文本识别
     * @param bitmap 处理后的图像
     */
    private fun processTextRecognition(bitmap: Bitmap) {
        try {
            // 初始化文本识别器
            val recognizer: TextRecognizer =
                TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
            val image: InputImage = InputImage.fromBitmap(bitmap, 0)

            // 使用CountDownLatch进行同步处理
            val latch: CountDownLatch = CountDownLatch(1)

            // 执行文本识别
            recognizer.process(image)
                .addOnSuccessListener { visionText ->
                    try {
                        // 处理识别结果
                        lineBoundingBoxes.clear()
                        for (block in visionText.getTextBlocks()) {
                            for (line in block.getLines()) {
                                lineBoundingBoxes.add(line)
                            }
                        }

                        // 使用YOLO模型检测MICR和CVN区域
                        val detections = yoloModelHandler?.detectRegions(bitmap)
                        if (detections != null && detections.isNotEmpty()) {
                            hasValidChequeContour = true
                            Log.d(TAG, "has valid cheque contour: $hasValidChequeContour")

                            // 处理检测结果
                            var foundMICR = false
                            var foundCVN = false
                            
                            for (detection in detections) {
                                val (box, confidence, clsId) = detection
                                when (clsId) {
                                    0 -> {
                                        processMICRRegion(box) // MICR
                                        foundMICR = true
                                    }
                                    1 -> {
                                        processCVNRegion(box)  // CVN
                                        foundCVN = true
                                    }
                                }
                            }
                            
                            // 记录未检测到的区域
                            if (!foundMICR) {
                                Log.e(TAG, "YOLO检测失败: 未能检测到MICR区域")
                                micrCode = null
                                micrScore = 0
                            }
                            if (!foundCVN) {
                                Log.e(TAG, "YOLO检测失败: 未能检测到CVN区域")
                                cvnCode = null
                                cvnScore = 0
                            }

                            // 检测签名
                            detectSignature()
                            
                            // 分析支票类型
                            identifyChequeType()
                        } else {
                            Log.e(TAG, "YOLO检测失败: 未能检测到任何区域")
                            hasValidChequeContour = false
                            // 重置所有识别结果
                            micrCode = null
                            micrScore = 0
                            cvnCode = null
                            cvnScore = 0
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "处理文本识别结果时出错: ${e.message}", e)
                        // 发生错误时重置所有结果
                        micrCode = null
                        micrScore = 0
                        cvnCode = null
                        cvnScore = 0
                    } finally {
                        latch.countDown()
                    }
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "文本识别过程出错: ${e.message}", e)
                    // 发生错误时重置所有结果
                    micrCode = null
                    micrScore = 0
                    cvnCode = null
                    cvnScore = 0
                    latch.countDown()
                }

            // 等待识别完成，设置较短的超时时间
            if (!latch.await(10, TimeUnit.SECONDS)) {
                Log.e(TAG, "文本识别超时")
                // 超时时重置所有结果
                micrCode = null
                micrScore = 0
                cvnCode = null
                cvnScore = 0
            }
        } catch (e: Exception) {
            Log.e(TAG, "文本识别失败: ${e.message}", e)
            // 发生异常时重置所有结果
            micrCode = null
            micrScore = 0
            cvnCode = null
            cvnScore = 0
        }
    }

    /**
     * Process MICR region // 处理MICR区域
     * @param box MICR区域的边界框
     */
    private fun processMICRRegion(box: Rect) {
        try {
            Log.d(TAG, "开始处理MICR区域...")
            
            if (processedBitmap == null) {
                Log.e(TAG, "图像未处理")
                return
            }

            // 使用检测到的边界框
            val micrBox = box
            Log.d(TAG, "获取到MICR边界框: $micrBox")

            // 验证边界框位置
            val imageHeight = processedBitmap!!.height
            if ((micrBox.centerY() * 100 / imageHeight) < 80) {
                Log.e(TAG, "MICR region not at the bottom of the image")
                return
            }

            // 从原图中裁剪出MICR区域
            val micrBitmap = Bitmap.createBitmap(
                processedBitmap!!,
                micrBox.left,
                micrBox.top,
                micrBox.width(),
                micrBox.height()
            )

            // 将Bitmap转换为OpenCV的Mat格式
            val micr = ChequeUtils.convertBitmapToMat(micrBitmap)

            // 将图像转换为灰度图
            val gray = Mat()
            val channels = micr.channels()
            
            try {
                when (channels) {
                    4 -> {
                        // 先转换为BGR，再转换为GRAY
                        val bgrMat = Mat()
                        Imgproc.cvtColor(micr, bgrMat, Imgproc.COLOR_RGBA2BGR)
                        Imgproc.cvtColor(bgrMat, gray, Imgproc.COLOR_BGR2GRAY)
                        bgrMat.release()
                    }
                    3 -> {
                        Imgproc.cvtColor(micr, gray, Imgproc.COLOR_BGR2GRAY)
                    }
                    1 -> {
                        micr.copyTo(gray)
                    }
                    else -> {
                        Log.e(TAG, "不支持的图像通道数: $channels")
                        throw IllegalStateException("不支持的图像通道数: $channels")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "颜色空间转换失败: ${e.message}")
                throw e
            }

            val rectKernel = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, Size(17.0, 7.0))
            val blackhat = Mat()
            Imgproc.morphologyEx(gray, blackhat, Imgproc.MORPH_BLACKHAT, rectKernel)

            val gradX = Mat()
            Imgproc.Sobel(blackhat, gradX, CvType.CV_32F, 1, 0)

            Core.convertScaleAbs(gradX, gradX)
            val minMax = Core.minMaxLoc(gradX)
            if (minMax.maxVal - minMax.minVal != 0.0) {
                Core.normalize(gradX, gradX, 0.0, 255.0, Core.NORM_MINMAX)
                gradX.convertTo(gradX, CvType.CV_8U)
            } else {
                gradX.setTo(Scalar(0.0))
            }

            Imgproc.morphologyEx(gradX, gradX, Imgproc.MORPH_CLOSE, rectKernel)
            val thresh = Mat()
            Imgproc.threshold(gradX, thresh, 0.0, 255.0, Imgproc.THRESH_BINARY or Imgproc.THRESH_OTSU)

            // Padding for border clearing
            val pad = 20
            val padded = Mat()
            Core.copyMakeBorder(thresh, padded, pad, pad, pad, pad, Core.BORDER_CONSTANT, Scalar(0.0))

            val threshCleaned = padded
            val croppedThresh = threshCleaned.submat(OpenCVRect(pad, pad, thresh.cols(), thresh.rows()))

            val contours = ArrayList<MatOfPoint>()
            val hierarchy = Mat()
            Imgproc.findContours(croppedThresh.clone(), contours as MutableList<MatOfPoint>, hierarchy, Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE)

            val h = gray.rows()
            val avgCharHeight = h / 10

            val groupRects = contours.map { Imgproc.boundingRect(it) }
//                .filter { it.width > 3 * avgCharHeight && it.height > avgCharHeight }
                .sortedBy { it.x }

            val chars = ArrayList<Mat>()
            val allLocs = ArrayList<OpenCVRect>()

            for ((rowIdx, groupRect) in groupRects.withIndex()) {
                val adjustedRect = OpenCVRect(
                    groupRect.x - 5,
                    groupRect.y - 5,
                    groupRect.width + 10,
                    groupRect.height + 10
                )

                val safeRect = clampRect(adjustedRect, gray.cols(), gray.rows())
                val roi = gray.submat(safeRect)

                val groupThresh = Mat()
                Imgproc.threshold(roi, groupThresh, 0.0, 255.0, Imgproc.THRESH_BINARY_INV or Imgproc.THRESH_OTSU)

                val charCnts = ArrayList<MatOfPoint>()
                Imgproc.findContours(groupThresh.clone(), charCnts as MutableList<MatOfPoint>, Mat(), Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE)

                // Sorting contours left-to-right manually
                java.util.Collections.sort(charCnts, Comparator<MatOfPoint> { a, b -> 
                    Imgproc.boundingRect(a).x - Imgproc.boundingRect(b).x 
                })

                try {
                    val (rois, locs) = extractDigitsAndSymbols(groupThresh, charCnts as java.util.List<MatOfPoint>)
                    for (roiChar in rois) {
                        val resized = Mat()
                        Imgproc.resize(roiChar, resized, Size(32.0, 32.0))
                        
                        // 在这里，将单通道图像转换为3通道
                        val colorRoi = Mat()
                        Imgproc.cvtColor(resized, colorRoi, Imgproc.COLOR_GRAY2BGR)
                        chars.add(colorRoi)
                    }
                    allLocs.addAll(locs)
                } catch (e: Exception) {
                    println("Skipping group due to error: ${e.message}")
                    continue
                }
            }

            MICRCodes.clear() // 清空之前的结果

            for (char in chars) {
                // Convert char image to grayscale
                val grayChar = Mat()
                Imgproc.cvtColor(char, grayChar, Imgproc.COLOR_BGR2GRAY)

                // Apply adaptive thresholding
                val binaryChar = Mat()
                val blockSize = 55
                val C = 8.0
                Imgproc.adaptiveThreshold(
                    grayChar, binaryChar, 255.0,
                    Imgproc.ADAPTIVE_THRESH_GAUSSIAN_C,
                    Imgproc.THRESH_BINARY_INV,
                    blockSize, C
                )

                // Convert back to BGR
                val bgrChar = Mat()
                Imgproc.cvtColor(binaryChar, bgrChar, Imgproc.COLOR_GRAY2BGR)

                // 将Mat格式转换为Bitmap
                val imgMat = ChequeUtils.convertMatToBitmap(bgrChar)
                if (imgMat != null) {
                    // 对字符图像进行填充处理并添加到结果列表
                    MICRCodes.add(padCharImg(ChequeUtils.convertBitmapToMat(imgMat)))
                }
            }

            // Verify MICR character count // 验证MICR字符数量
            val (recognizedMicr, confidence) = tfLiteHandler!!.execute()
            validMicrPosition = true
            Log.d(TAG, "valid micr position: $validMicrPosition")
            
            // 前置判断：从前五位开始检测，当识别到当前位是","且后一位是数字时，将该","位前面的字符删去
            var processedMicr = recognizedMicr
            var foundValidComma = false

            // 只检查前五位
            for (i in 0 until minOf(5, processedMicr.length - 1)) {
                if (processedMicr[i] == ',' && processedMicr[i + 1].isDigit()) {
                    processedMicr = processedMicr.substring(i)
                    foundValidComma = true
                    Log.d(TAG, "valid micr: $processedMicr")
                    break
                }
            }

            // 如果前五位没有找到有效组合，则输出原MICR码，并将micrScore设为0
            if (!foundValidComma) {
                micrScore = (confidence * 1000 / 10).toInt()
                Log.d(TAG, "invalid micr: $processedMicr")
            }

            while (processedMicr.isNotEmpty() && !(processedMicr.last() == ',' || processedMicr.last().isDigit())) {
                processedMicr = processedMicr.substring(0, processedMicr.length - 1)
                Log.d(TAG, "valid micr: $processedMicr")
            }
            
            // 使用处理后的MICR码长度进行判断
            if (processedMicr.length == 25 || processedMicr.length == 27 || processedMicr.length == 28 || processedMicr.length == 29) {
                micrCode = processedMicr
                
                // 检查是否包含5位以上连续相同数字
                var maxCount = 1
                var count = 1
                var currentChar = processedMicr[0]
                
                for (i in 1 until processedMicr.length) {
                    if (processedMicr[i] == currentChar) {
                        count++
                        maxCount = maxOf(maxCount, count)
                    } else {
                        count = 1
                        currentChar = processedMicr[i]
                    }
                }
                
                // 根据检查结果设置分数
                micrScore = if (maxCount > 5) {
                    (confidence * 1000 / 5).toInt()
                } else {
                    (confidence * 1000).toInt()
                }
            } else {
                micrCode = processedMicr
                micrScore = (confidence * 1000 / 10).toInt()
                Log.e(
                    TAG,
                    "Incorrect MICR character count: ${processedMicr.length} // MICR字符数量不正确: ${processedMicr.length}"
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理MICR区域时出错: ${e.message}")
            Log.e(TAG, "详细错误信息: ", e)
            micrScore = 0
        }
    }

    /**
     * 处理CVN区域
     * @param box CVN区域的边界框
     */
    private fun processCVNRegion(box: Rect) {
        try {
            // 存储CVN区域
            this.cvnRect = box

            // 从原图中裁剪出CVN区域
            val originalBitmap = Bitmap.createBitmap(
                processedBitmap!!,
                box.left,
                box.top,
                box.width(),
                box.height()
            )

            // 确保图像尺寸足够大
            val minSize = 32
            val width = originalBitmap.width
            val height = originalBitmap.height
            
            // 如果图像太小，进行适当的放大
            val cvnBitmap = if (width < minSize || height < minSize) {
                // 计算缩放比例，保持宽高比
                val scale = maxOf(minSize.toFloat() / width, minSize.toFloat() / height)
                val newWidth = (width * scale).toInt()
                val newHeight = (height * scale).toInt()
                Bitmap.createScaledBitmap(originalBitmap, newWidth, newHeight, true)
            } else {
                originalBitmap
            }

            // 使用MLKit进行文本识别
            val recognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
            val image = InputImage.fromBitmap(cvnBitmap, 0)

            // 使用CountDownLatch来等待后台线程完成
            val latch = CountDownLatch(1)

            // 在后台线程中执行文本识别
            val executorService = Executors.newSingleThreadExecutor()
            executorService.execute {
                try {
                    // 在后台线程中执行文本识别
                    val visionText = Tasks.await(
                        recognizer.process(image),
                        5, // 超时时间
                        TimeUnit.SECONDS // 时间单位
                    )

                    // 记录每个元素的confidence
                    var totalConfidence = 0f
                    var elementCount = 0

                    // 处理识别结果
                    for (block in visionText.textBlocks) {
                        for (line in block.lines) {
                            for (element in line.elements) {
                                totalConfidence += element.confidence ?: 0f
                                elementCount++
                            }
                        }
                    }

                    // 过滤出数字
                    cvnCode = visionText.text.filter { it.isDigit() }
                    
                    // 计算平均confidence并转换为分数
                    val averageConfidence = if (elementCount > 0) totalConfidence / elementCount else 0f
                    cvnScore = if (cvnCode?.isNotEmpty() == true) {
                        (averageConfidence * 1000).toInt()
                    } else {
                        0
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "CVN文本识别失败或超时: ${e.message}")
                    cvnScore = 0
                    cvnCode = null
                } finally {
                    latch.countDown()
                    // 释放资源
                    if (cvnBitmap !== originalBitmap) {
                        cvnBitmap.recycle()
                    }
                    originalBitmap.recycle()
                }
            }

            // 等待后台任务完成，设置超时时间
            if (!latch.await(7, TimeUnit.SECONDS)) {
                Log.e(TAG, "CVN处理超时")
                cvnScore = 0
                cvnCode = null
            }

            // 关闭线程池
            executorService.shutdown()

        } catch (e: Exception) {
            Log.e(TAG, "处理CVN区域时出错: ${e.message}", e)
            cvnScore = 0
            cvnCode = null
        }
    }

    /**
     * 检测签名
     * @return 是否检测到签名
     */
    fun detectSignature(): Pair<Boolean, Int> {
        try {
            if (processedBitmap == null) {
                Log.e(TAG, "无法检测签名，未处理图像")
                signatureScore = 0
                return Pair(false, 0)
            }

            // 获取签名区域
            signatureRect = signatureRegion

            // 裁剪签名区域
            val signatureArea: Bitmap = Bitmap.createBitmap(
                processedBitmap!!,
                signatureRect!!.left,
                signatureRect!!.top,
                signatureRect!!.width(),
                signatureRect!!.height()
            )

            // 转换为灰度图并进行二值化检测
            val signatureMat: Mat = Mat()
            Utils.bitmapToMat(signatureArea, signatureMat)

            // 转换为灰度图
            val grayMat: Mat = Mat()
            Imgproc.cvtColor(signatureMat, grayMat, Imgproc.COLOR_BGR2GRAY)

            // 应用高斯模糊
            val blurredMat: Mat = Mat()
            Imgproc.GaussianBlur(grayMat, blurredMat, Size(5.0, 5.0), 0.0)

            // 应用二值化
            val thresholdMat: Mat = Mat()
            Imgproc.threshold(
                blurredMat,
                thresholdMat,
                0.0,
                255.0,
                Imgproc.THRESH_BINARY_INV or Imgproc.THRESH_OTSU
            )

            // 形态学操作去除噪点
            val morphMat: Mat = Mat()
            val kernel: Mat = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, Size(3.0, 3.0))
            Imgproc.morphologyEx(thresholdMat, morphMat, Imgproc.MORPH_OPEN, kernel)

            // 计算黑色像素占比
            val totalPixels: Int = morphMat.width() * morphMat.height()
            val blackPixels: Int = Core.countNonZero(morphMat)
            val blackPixelRatio = blackPixels.toDouble() / totalPixels

            // 计算签名复杂度分数 (基于轮廓)
            val contours = ArrayList<MatOfPoint>() 
            val hierarchy = Mat()
            Imgproc.findContours(
                morphMat.clone(),
                contours,
                hierarchy,
                Imgproc.RETR_EXTERNAL,
                Imgproc.CHAIN_APPROX_SIMPLE
            )
            
            // 计算轮廓复杂度得分 (0-400分)
            val contoursScore = calculateContoursScore(contours)
            
            // 计算笔画连续性得分 (0-300分)
            val strokeScore = calculateStrokeScore(morphMat)
            
            // 计算像素分布得分 (0-300分)
            val pixelScore = calculatePixelDistributionScore(blackPixelRatio)

            // 释放资源
            signatureMat.release()
            grayMat.release()
            blurredMat.release()
            thresholdMat.release()
            morphMat.release()
            kernel.release()
            hierarchy.release()
            contours.forEach { it.release() }

            // 判断是否存在签名 (若黑色像素占比在合理范围内则认为存在签名)
            hasSignature = blackPixelRatio > 0.025 && blackPixelRatio < 0.30

            // 计算总分 (满分1000分)
            signatureScore = if (hasSignature) {
                (contoursScore + strokeScore + pixelScore).coerceIn(0, 1000)
            } else {
                (contoursScore + strokeScore + pixelScore).coerceIn(0, 1000)
            }

            Log.d(TAG, "signature result: " + (if (hasSignature) "detected" else "not detected"))
            Log.d(TAG, "blackPixelRatio: " + (blackPixelRatio * 100) + "%")
            Log.d(TAG, "signature score: $signatureScore ($contoursScore, $strokeScore, $pixelScore)")

            return Pair(hasSignature, signatureScore)
        } catch (e: Exception) {
            Log.e(TAG, "签名检测失败: ${e.message}", e)
            hasSignature = false
            signatureScore = 0
            return Pair(false, 0)
        }
    }

    /**
     * 计算轮廓复杂度得分
     * @param contours 轮廓列表
     * @return 0-400的得分
     */
    private fun calculateContoursScore(contours: ArrayList<MatOfPoint>): Int {
        if (contours.isEmpty()) return 0
        
        // 计算轮廓的总周长和面积
        var totalPerimeter = 0.0
        var totalArea = 0.0
        contours.forEach { contour ->
            val contour2f = MatOfPoint2f()
            contour.convertTo(contour2f, CvType.CV_32F)
            totalPerimeter += Imgproc.arcLength(contour2f, true)
            totalArea += Imgproc.contourArea(contour)
            contour2f.release()
        }
        
        // 计算复杂度指标
        val complexity = if (totalArea > 0) totalPerimeter * totalPerimeter / totalArea else 0.0
        
        // 将复杂度映射到0-400分
        return (complexity.coerceIn(0.0, 1000.0) * 0.4).toInt()
    }

    /**
     * 计算笔画连续性得分
     * @param morphMat 处理后的图像矩阵
     * @return 0-300的得分
     */
    private fun calculateStrokeScore(morphMat: Mat): Int {
        // 计算水平和垂直方向的像素变化
        val rows = morphMat.rows()
        val cols = morphMat.cols()
        var transitions = 0
        
        // 统计像素变化次数
        for (i in 0 until rows) {
            var lastPixel = 0.0
            for (j in 0 until cols) {
                val pixel = morphMat.get(i, j)[0]
                if (pixel != lastPixel) {
                    transitions++
                }
                lastPixel = pixel
            }
        }
        
        // 计算平均变化率
        val maxTransitions = rows * cols
        val transitionRate = transitions.toDouble() / maxTransitions
        
        // 将变化率映射到0-300分
        return ((1.0 - transitionRate.coerceIn(0.0, 1.0)) * 300).toInt()
    }

    /**
     * 计算像素分布得分
     * @param blackPixelRatio 黑色像素占比
     * @return 0-300的得分
     */
    private fun calculatePixelDistributionScore(blackPixelRatio: Double): Int {
        // 理想的黑色像素占比范围是2.5%-30%
        // 根据像素比例计算得分
        return when {
            blackPixelRatio < 0.025 -> 0
            blackPixelRatio > 0.30 -> 0
            blackPixelRatio < 0.05 -> ((blackPixelRatio - 0.025) * 300 / 0.025).toInt()
            blackPixelRatio < 0.15 -> 300
            else -> ((0.30 - blackPixelRatio) * 300 / 0.15).toInt()
        }
    }

    /**
     * 获取签名评分
     * @return 签名评分 (0-1000)
     */
    fun getSignatureScore(): Int {
        return signatureScore
    }

    private val signatureRegion: Rect
        /**
         * 获取签名区域
         * @return 签名区域的Rect
         */
        get() {
            val width: Int = processedBitmap!!.width
            val height: Int = processedBitmap!!.height

            // 使用固定比例获取签名区域
            return Rect(
                (width * 0.58).toInt(),  // left
                (height * 0.55).toInt(),  // top
                (width * 0.99).toInt(),  // right
                (height * 0.85).toInt() // bottom
            )
        }

    /**
     * 识别支票类型
     * @return 支票类型描述 ("Real Cheque" 或 "Printed Cheque")
     */
    fun identifyChequeType(): String {
        try {
            if (processedMat == null || processedMat!!.empty()) {
                Log.e(TAG, "无法识别支票类型，未处理图像")
                return "Unknown"
            }

            // 使用ONNX模型进行判断 (0表示打印支票，1表示真实支票)
            val result: Int = onnxModelHandler!!.predict(processedMat!!)
            isGenuine = (result == 1) // 更新真伪状态

            val typeResult = if ((result == 1)) "Real Cheque" else "Printed Cheque"
            Log.d(
                TAG,
                "支票类型识别结果: $typeResult"
            )

            return typeResult
        } catch (e: Exception) {
            Log.e(TAG, "支票类型识别失败: ${e.message}", e)
            return "Unknown"
        }
    }

    val isGenuineCheque: Boolean
        /**
         * 判断支票是否真实
         * @return 是否为真实支票
         */
        get() {
            if (processedMat == null || processedMat!!.empty()) {
                identifyChequeType() // 如果未分析，先执行分析
            }
            return isGenuine
        }

    /**
     * 获取MICR码和分数
     * @return Pair<String?, Int> MICR码和分数
     */
    fun recognizeMICRCode(): Pair<String?, Int> {
        // 直接返回已有的结果，不再尝试重新检测
        return Pair(micrCode, micrScore)
    }

    /**
     * 获取CVN码和分数
     * @return Pair<String?, Int> CVN码和分数
     */
    fun recognizeCVNCode(): Pair<String?, Int> {
        // 直接返回已有的结果，不再尝试重新检测
        return Pair(cvnCode, cvnScore)
    }

    val signatureResult: String
        /**
         * 获取签名检测结果
         * @return "Detected" 或 "Not Detected"
         */
        get() {
            if (processedBitmap == null) {
                Log.e(
                    TAG,
                    "请先调用processChequeImage方法处理图像"
                )
                return "Not Detected"
            }

            if (signatureRect == null) {
                detectSignature()
            }

            return if (hasSignature) "Detected" else "Not Detected"
        }

    /**
     * 旋转支票图像
     * @param degrees 旋转角度
     * @return 旋转后的图像
     */
    fun rotateChequeImage(degrees: Int): Bitmap? {
        if (processedBitmap == null) {
            Log.e(TAG, "请先调用processChequeImage方法处理图像")
            return null
        }

        try {
            return ChequeUtils.rotateBitmap(processedBitmap, degrees)
        } catch (e: Exception) {
            Log.e(TAG, "旋转图像时出错: ${e.message}", e)
            return null
        }
    }

    val resultVisualization: Bitmap?
        /**
         * 获取结果可视化图像
         * @return 带有识别结果标记的图像
         */
        get() {
            if (processedBitmap == null) {
                Log.e(
                    TAG,
                    "请先调用processChequeImage方法处理图像"
                )
                return null
            }

            try {
                // 创建可变Bitmap用于绘制
                val resultBitmap = processedBitmap!!.copy(Bitmap.Config.ARGB_8888, true)
                val canvas = Canvas(resultBitmap)

                // 创建画笔
                val paintText = Paint().apply {
                    color = Color.WHITE
                    textSize = 60f
                    style = Paint.Style.FILL
                }

                val paintSuccess = Paint().apply {
                    style = Paint.Style.STROKE
                    strokeWidth = 5f
                    color = Color.GREEN
                }

                val paintFail = Paint().apply {
                    style = Paint.Style.STROKE
                    strokeWidth = 5f
                    color = Color.RED
                }

                val textBackground = Paint().apply {
                    color = Color.BLACK
                    alpha = 160 // 半透明黑色背景
                    style = Paint.Style.FILL
                }

                // 绘制MICR区域
                micrRect?.let { rect ->
                    canvas.drawRect(rect, if (micrCode != null) paintSuccess else paintFail)

                    // 添加MICR标签
                    val micrLabel = "MICR: ${micrCode ?: "Not Detected"}"
                    val textWidth = paintText.measureText(micrLabel)
                    val textBounds = Rect()
                    paintText.getTextBounds(micrLabel, 0, micrLabel.length, textBounds)

                    // 绘制MICR标签和背景
                    val labelX = rect.left
                    val labelY = rect.top - 10
                    canvas.drawRect(
                        labelX.toFloat(), (labelY - textBounds.height()).toFloat(),
                        labelX + textWidth + 10f, labelY + 10f, textBackground
                    )
                    canvas.drawText(micrLabel, labelX + 5f, labelY.toFloat(), paintText)
                }

                // 绘制CVN区域
                cvnRect?.let { rect ->
                    canvas.drawRect(rect, if (cvnCode != null) paintSuccess else paintFail)

                    // 添加CVN标签
                    val cvnLabel = "CVN: ${cvnCode ?: "Not Detected"}"
                    val textWidth = paintText.measureText(cvnLabel)
                    val textBounds = Rect()
                    paintText.getTextBounds(cvnLabel, 0, cvnLabel.length, textBounds)

                    // 绘制CVN标签和背景
                    val labelX = rect.left
                    val labelY = rect.top - 10
                    canvas.drawRect(
                        labelX.toFloat(), (labelY - textBounds.height()).toFloat(),
                        labelX + textWidth + 10f, labelY + 10f, textBackground
                    )
                    canvas.drawText(cvnLabel, labelX + 5f, labelY.toFloat(), paintText)
                }

                // 绘制签名区域
                signatureRect?.let { rect ->
                    canvas.drawRect(rect, if (hasSignature) paintSuccess else paintFail)

                    // 添加签名标签
                    val signatureLabel = "Signature: ${if (hasSignature) "Detected" else "Not Detected"}"
                    val textWidth = paintText.measureText(signatureLabel)
                    val textBounds = Rect()
                    paintText.getTextBounds(signatureLabel, 0, signatureLabel.length, textBounds)

                    // 绘制签名标签和背景
                    val labelX = rect.left
                    val labelY = rect.top - 10
                    canvas.drawRect(
                        labelX.toFloat(), (labelY - textBounds.height()).toFloat(),
                        labelX + textWidth + 10f, labelY + 10f, textBackground
                    )
                    canvas.drawText(signatureLabel, labelX + 5f, labelY.toFloat(), paintText)
                }

                // 在顶部居中位置显示支票类型
                val chequeTypeResult = identifyChequeType()
                val textWidth = paintText.measureText(chequeTypeResult)
                val textBounds = Rect()
                paintText.getTextBounds(chequeTypeResult, 0, chequeTypeResult.length, textBounds)

                val centerX = resultBitmap.width / 2
                val topY = 100

                // 绘制支票类型标签和背景
                canvas.drawRect(
                    centerX - textWidth / 2 - 10f, topY - textBounds.height() - 10f,
                    centerX + textWidth / 2 + 10f, topY + 10f, textBackground
                )
                canvas.drawText(chequeTypeResult, centerX - textWidth / 2, topY.toFloat(), paintText)

                return resultBitmap
            } catch (e: Exception) {
                Log.e(
                    TAG,
                    "生成结果可视化图像时出错: ${e.message}",
                    e
                )
                return processedBitmap // 出错时返回原图
            }
        }

    /**
     * 获取MICR区域
     * @return MICR区域的Rect
     */
    val micrRegion: Rect?
        get() = micrRect

    /**
     * 获取CVN区域
     * @return CVN区域的Rect
     */
    val cvnRegion: Rect?
        get() = cvnRect

    /**
     * 获取签名区域
     * @return 签名区域的Rect
     */
    fun getDetectedSignatureRegion(): Rect? {
        return signatureRect
    }
    
    /**
     * 获取CVN码
     * @return CVN码
     */
    fun getCVNCode(): String? {
        return cvnCode
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            // 清除处理结果
            clearPreviousResults()

            // 释放模型
            if (onnxModelHandler != null) {
                onnxModelHandler!!.close()
                onnxModelHandler = null
            }

            if (tfLiteHandler != null) {
                tfLiteHandler!!.close()
                tfLiteHandler = null
            }

            // 清除原始图像
            if (originalBitmap != null && !originalBitmap!!.isRecycled) {
                originalBitmap!!.recycle()
                originalBitmap = null
            }

            if (segmentedBitmap != null && !segmentedBitmap!!.isRecycled) {
                segmentedBitmap!!.recycle()
                segmentedBitmap = null
            }

            instance = null
        } catch (e: Exception) {
            Log.e(TAG, "释放SDK资源时出错: ${e.message}", e)
        }
    }

    /**
     * 对图像进行预处理
     * @param frame 输入图像
     * @return 预处理后的图像
     */
    private fun preProcessFrame(frame: Mat): Mat {
        // morph to remove content inside the cheque
        val kernel = Mat.ones(Size(5.0, 5.0), CvType.CV_8U)
        val morph = Mat()
        Imgproc.morphologyEx(frame, morph, Imgproc.MORPH_CLOSE, kernel, Point(-1.0, -1.0), 5)

        // make the mat to gray
        val gray = Mat()
        Imgproc.cvtColor(morph, gray, Imgproc.COLOR_BGR2GRAY)

        // threshold to remove unnecessary pixels
        var blocks = 22
        blocks = if (blocks % 2 == 1) blocks else blocks + 1
        val constant = 8.0
        val adaptive = Mat()
        Imgproc.adaptiveThreshold(
            gray,
            adaptive,
            255.0,
            Imgproc.ADAPTIVE_THRESH_GAUSSIAN_C,
            Imgproc.THRESH_BINARY,
            blocks,
            constant
        )

        val blurSize = Size(11.0, 11.0)
        val blur = Mat()
        Imgproc.GaussianBlur(adaptive, blur, blurSize, 0.0)

        return blur
    }

    /**
     * 提取图像中的轮廓
     * @param frame 预处理后的图像
     * @return 轮廓列表
     */
    private fun extractContours(frame: Mat): List<MatOfPoint> {
        // contour detection
        val thres1 = 120.0
        val thres2 = 170.0
        val frameEdges = Mat()
        Imgproc.Canny(frame, frameEdges, thres1, thres2)

        val dilateKernel = Imgproc.getStructuringElement(Imgproc.MORPH_RECT, Size(5.0, 5.0))
        val frameDil = Mat()
        Imgproc.dilate(frameEdges, frameDil, dilateKernel, Point(-1.0, -1.0), 1)

        val contours: ArrayList<MatOfPoint> = ArrayList()
        val hierarchy = Mat()
        Imgproc.findContours(
            frameEdges,
            contours,
            hierarchy,
            Imgproc.RETR_EXTERNAL,
            Imgproc.CHAIN_APPROX_NONE
        )

        return contours as List<MatOfPoint>
    }

    /**
     * 选择支票轮廓
     * @param contours 轮廓列表
     * @return 支票轮廓列表
     */
    private fun selectChequeContour(contours: List<MatOfPoint>): List<MatOfPoint> {
        //get the largest contour
        var maxArea = 0.0 //filter out small cheques
        var selectedcnt: MatOfPoint? = null

        for (contour in contours) {
            val area: Double = Imgproc.contourArea(contour)
            if (area > maxArea) {
                maxArea = area
                selectedcnt = contour
            }
        }

        val hull = MatOfInt()
        var hullPoint: MatOfPoint? = null
        var boxPoint: MatOfPoint? = null

        if (selectedcnt != null) {
            // 创建包含selectedcnt点的MatOfPoint2f
            val selectedcntPoints2f = MatOfPoint2f()
            selectedcnt.convertTo(selectedcntPoints2f, CvType.CV_32F)
            
            val peri: Double = Imgproc.arcLength(selectedcntPoints2f, true)
            val approx = MatOfPoint2f()
            Imgproc.approxPolyDP(selectedcntPoints2f, approx, 0.02 * peri, true)

            val contour = MatOfPoint()
            approx.convertTo(contour, CvType.CV_32S)
            Imgproc.convexHull(contour, hull)

            hullPoint = MatOfPoint()
            val hullHeight = hull.size().height.toInt()
            hullPoint.create(hullHeight, 1, CvType.CV_32SC2)

            for (j in 0 until hullHeight) {
                val index = hull.get(j, 0)[0].toInt()
                val point = doubleArrayOf(
                    approx.get(index, 0)[0], approx.get(index, 0)[1]
                )
                hullPoint.put(j, 0, point[0], point[1])
            }

            // 为minAreaRect创建MatOfPoint2f
            val hullPointFloat = MatOfPoint2f()
            hullPoint.convertTo(hullPointFloat, CvType.CV_32F)
            val rect: RotatedRect = Imgproc.minAreaRect(hullPointFloat)

            // 创建一个包含4个点的数组
            val pointsArray = Array(4) { Point() }
            rect.points(pointsArray)

            // 将数组转换为MatOfPoint
            boxPoint = MatOfPoint(*pointsArray)
        }

        val resultList: ArrayList<MatOfPoint> = ArrayList()

        if (hullPoint != null && hullPoint.rows() == 4) {
            resultList.add(hullPoint)
        } else if (boxPoint != null) {
            resultList.add(boxPoint)
        }

        return resultList as List<MatOfPoint>
    }

    /**
     * 对MICR字符图像进行填充，使其居中
     */
    private fun padCharImg(originalImage: Mat): Mat {
        val origHeight = originalImage.rows()
        val origWidth = originalImage.cols()
        val desiredSize = max(origHeight, origWidth) + 60

        val padHeight = max(desiredSize - origHeight, 0) / 2
        val padWidth = max(desiredSize - origWidth, 0) / 2

        val paddedImage = Mat()
        Core.copyMakeBorder(
            originalImage,
            paddedImage,
            padHeight,
            padHeight,
            padWidth,
            padWidth,
            Core.BORDER_CONSTANT,
            Scalar(255.0, 255.0, 255.0)
        )

        return paddedImage
    }

    /**
     * 根据X轴投影生成窗口
     * @param X X轴投影数组
     * @return 窗口列表
     */
    private fun generateWindows(X: IntArray): List<IntArray> {
        val windowsList: ArrayList<IntArray> = ArrayList()
        var wd = intArrayOf(-1, -1)
        var s = false

        for (i in X.indices) {
            if (X[i] == 0 && !s) {
                continue
            }
            if (X[i] == 0 && s) {
                wd[1] = i
                windowsList.add(wd)
                wd = intArrayOf(-1, -1)
                s = false
            }
            if (X[i] > 0 && !s) {
                s = true
                wd[0] = i
            }
            if (X[i] > 0 && s) {
                continue
            }
        }
        
        // 处理数组末尾仍有未结束的窗口的情况
        if (s && wd[0] != -1) {
            wd[1] = X.size
            windowsList.add(wd)
        }

        return windowsList as List<IntArray>
    }

    /**
     * 合并相邻的窗口
     * @param windows 待合并的窗口列表
     * @return 合并后的窗口列表
     */
    private fun merge_windows(windows: List<IntArray>): List<IntArray> {
        if (windows.isEmpty()) {
            return ArrayList<IntArray>() as List<IntArray>
        }
        
        val wdxLens = windows.map { it[1] - it[0] }
        val meanLen = wdxLens.average().toInt()

        val sortedWindowsList: ArrayList<IntArray> = ArrayList(windows)
        Collections.sort(sortedWindowsList, Comparator.comparingInt { it[0] })
        
        val mergedList: ArrayList<IntArray> = ArrayList()
        val toBeMergedList: ArrayList<IntArray> = ArrayList()
        
        for (window in sortedWindowsList) {
            if (winLen(window) < meanLen) {
                toBeMergedList.add(window)
            } else {
                if (toBeMergedList.isNotEmpty()) {
                    val currentWindow = intArrayOf(
                        toBeMergedList[0][0],
                        toBeMergedList[toBeMergedList.size - 1][1]
                    )
                    mergedList.add(currentWindow)
                    Log.d(
                        TAG, "merge: ${toBeMergedList.map { winLen(it) }} -> ${winLen(currentWindow)}"
                    )
                    toBeMergedList.clear()
                }
                mergedList.add(window)
            }
        }

        if (toBeMergedList.isNotEmpty()) {
            val currentWindow = intArrayOf(toBeMergedList[0][0], toBeMergedList[toBeMergedList.size - 1][1])
            mergedList.add(currentWindow)
            Log.d(
                TAG, "merge: ${toBeMergedList.map { winLen(it) }} -> ${winLen(currentWindow)}"
            )
        }

        return mergedList as List<IntArray>
    }

    /**
     * 计算窗口长度
     * @param window 窗口
     * @return 窗口长度
     */
    private fun winLen(window: IntArray): Int {
        return window[1] - window[0]
    }

    /**
     * 计算图像X轴方向的投影
     * @param imgB 二值化图像
     * @return X轴投影数组
     */
    private fun xShadow(imgB: Mat): IntArray {
        val h: Int = imgB.rows()
        val w: Int = imgB.cols()
        val a = IntArray(w)

        for (i in 0 until h) {
            for (j in 0 until w) {
                val pixel: DoubleArray = imgB.get(i, j)
                if (pixel[0] == 255.0) {
                    a[j]++
                }
            }
        }

        return a
    }

    /**
     * 计算图像Y轴方向的投影
     * @param imgB 二值化图像
     * @return Y轴投影数组
     */
    private fun yShadow(imgB: Mat): IntArray {
        val h: Int = imgB.rows()
        val w: Int = imgB.cols()
        val a = IntArray(h)

        for (i in 0 until h) {
            for (j in 0 until w) {
                val pixel: DoubleArray = imgB.get(i, j)
                if (pixel[0] == 255.0) {
                    a[i]++
                }
            }
        }

        return a
    }

    /**
     * 根据轮廓裁剪图像
     * @param frame 原始图像
     * @param cnt 轮廓
     * @return 裁剪后的图像
     */
    private fun cropCnt(frame: Mat, cnt: MatOfPoint): Mat {
        val newFrame: Mat = Mat()
        Imgproc.cvtColor(frame, newFrame, Imgproc.COLOR_BGR2RGBA)

        val destImage: Mat = Mat(720, 1320, frame.type())
        val points = cnt.toList()
        
        // 按x坐标排序
        val sortedByX = points.sortedWith(compareBy<Point> { it.x }.thenBy { it.y })
        var sumX = 0.0
        var sumY = 0.0

        for (point in points) {
            sumX += point.x
            sumY += point.y
        }

        val numPoints: Int = points.size
        val centerX = sumX / numPoints
        val centerY = sumY / numPoints

        val center = Point(centerX, centerY)

        // 按顺时针排序
        val sortedPoints = points.sortedWith(ClockwiseComparator(center))
        
        // 确保有足够的点
        if (sortedPoints.size < 4) {
            // 如果点不足，返回原图的副本
            Log.e(TAG, "点数不足，无法进行透视变换")
            val result = Mat()
            frame.copyTo(result)
            return result
        }
        
        // 创建源点矩阵 - 使用前4个点
        val srcPoints = sortedPoints.take(4).toTypedArray()
        val src = MatOfPoint2f()
        src.fromArray(*srcPoints)

        // 创建目标点矩阵
        val dstPoints = arrayOf(
            Point(0.0, 0.0),
            Point(destImage.width() - 1.0, 0.0),
            Point(destImage.width() - 1.0, destImage.height() - 1.0),
            Point(0.0, destImage.height() - 1.0)
        )
        val dst = MatOfPoint2f()
        dst.fromArray(*dstPoints)
        
        // 执行透视变换
        val transform: Mat = Imgproc.getPerspectiveTransform(src, dst)
        Imgproc.warpPerspective(newFrame, destImage, transform, destImage.size())
        return destImage
    }

    /**
     * 如果图像太大，进行缩放处理
     * @param bitmap 原始图像
     * @param maxDimension 最大尺寸
     * @return 缩放后的图像
     */
    private fun scaleBitmapIfNeeded(bitmap: Bitmap?, maxDimension: Int): Bitmap? {
        if (bitmap == null) return null

        val width: Int = bitmap.width
        val height: Int = bitmap.height

        if (width <= maxDimension && height <= maxDimension) {
            return bitmap // 无需缩放
        }
        val scale = if (width > height) {
            maxDimension.toFloat() / width
        } else {
            maxDimension.toFloat() / height
        }

        val newWidth: Int = Math.round(width * scale)
        val newHeight: Int = Math.round(height * scale)

        Log.d(TAG, String.format("缩放图像从 %dx%d 到 %dx%d", width, height, newWidth, newHeight))

        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }

    /**
     * 获取 MICR 可视化结果
     * @return MICR 可视化的 Bitmap，如果未处理或处理失败则返回 null
     */
    fun getMICRVisualization(): Bitmap? {
        
        if (!MICRCodes.isEmpty()) {
            MICRCodes.forEachIndexed { index, mat ->
                Log.d(TAG, "MICRCodes[$index]: 是否为null=${mat == null}, 是否为空=${if (mat != null) mat.empty() else "mat为null"}")
            }
        } else {
            Log.e(TAG, "MICRCodes 为空列表")
        }
        
        if (processedBitmap == null || MICRCodes.isEmpty()) {
            Log.e(TAG, "无法生成 MICR 可视化结果：processedBitmap=${processedBitmap == null}, MICRCodes.isEmpty=${MICRCodes.isEmpty()}, MICRCodes.size=${MICRCodes.size}")
            return null
        }
        
        
        try {
            // 计算布局
            val padding = 20  // 字符之间的间距
            val charHeight = 100  // 字符显示高度
            val charWidth = 100   // 字符显示宽度
            
            val totalWidth = (charWidth + padding) * MICRCodes.size - padding
            
            // 创建一个新的 Bitmap，宽度为所有字符的总宽度加间距，高度固定
            val visualizationBitmap = Bitmap.createBitmap(
                (charWidth + padding) * MICRCodes.size - padding,  // 总宽度
                charHeight,  // 固定高度
                Bitmap.Config.ARGB_8888
            )
            
            val canvas = Canvas(visualizationBitmap)
            
            // 设置白色背景
            canvas.drawColor(Color.WHITE)

            // 遍历每个 MICR 字符
            for (i in 0 until MICRCodes.size) {
                val charMat = MICRCodes[i]
                
                if (!charMat.empty()) {
                    // 将 Mat 转换为 Bitmap
                    val charBitmap = ChequeUtils.convertMatToBitmap(charMat)
                    if (charBitmap != null) {
                        
                        // 计算缩放比例，保持宽高比
                        val scaleWidth = charWidth.toFloat() / charBitmap.width
                        val scaleHeight = charHeight.toFloat() / charBitmap.height
                        val scale = minOf(scaleWidth, scaleHeight)

                        // 创建变换矩阵
                        val matrix = Matrix()
                        matrix.postScale(scale, scale)

                        // 计算居中位置
                        val scaledWidth = charBitmap.width * scale
                        val scaledHeight = charBitmap.height * scale
                        val x = i * (charWidth + padding) + (charWidth - scaledWidth) / 2
                        val y = (charHeight - scaledHeight) / 2

                        // 创建源和目标矩形
                        val srcRect = Rect(0, 0, charBitmap.width, charBitmap.height)
                        val dstRect = Rect(
                            x.toInt(),
                            y.toInt(),
                            (x + scaledWidth).toInt(),
                            (y + scaledHeight).toInt()
                        )

                        // 绘制字符
                        canvas.drawBitmap(charBitmap, srcRect, dstRect, Paint().apply {
                            isFilterBitmap = true
                            isAntiAlias = true
                        })

                        // 回收临时 Bitmap
                        charBitmap.recycle()
                    } else {
                        Log.e(TAG, "字符 ${i + 1} 转换为Bitmap失败")
                    }
                } else {
                    Log.e(TAG, "字符 ${i + 1} 的Mat是空的")
                }
            }

            Log.d(TAG, "MICR可视化处理完成")
            return visualizationBitmap
        } catch (e: Exception) {
            Log.e(TAG, "生成 MICR 可视化结果时出错: ${e.message}")
            e.printStackTrace()
            return null
        }
    }

    /**
     * 保存MICR区域图像
     * @param context Context对象
     * @return 保存的文件路径，如果保存失败则返回null
     */
    fun saveMICRImage(context: Context): String? {
        if (processedBitmap == null || micrRect == null) {
            Log.e(TAG, "无法保存MICR图像：未处理图像或未检测到MICR区域")
            return null
        }

        try {
            // 从原图中裁剪出MICR区域
            val micrBitmap = Bitmap.createBitmap(
                processedBitmap!!,
                micrRect!!.left,
                micrRect!!.top,
                micrRect!!.width(),
                micrRect!!.height()
            )

            // 创建保存目录
            val directory = File(context.getExternalFilesDir(null), "micr_images")
            Log.d(TAG, "MICR图像保存目录的完整路径: ${directory.absolutePath}")
            
            if (!directory.exists()) {
                val created = directory.mkdirs()
                Log.d(TAG, "创建目录${if (created) "成功" else "失败"}: ${directory.absolutePath}")
            } else {
                Log.d(TAG, "目录已存在: ${directory.absolutePath}")
            }

            // 生成文件名（使用时间戳）
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "micr_$timestamp.jpg"
            val file = File(directory, fileName)

            // 保存图像
            FileOutputStream(file).use { out ->
                micrBitmap.compress(Bitmap.CompressFormat.JPEG, 100, out)
            }

            // 验证文件是否成功创建
            if (file.exists()) {
                Log.d(TAG, "MICR图像成功保存到: ${file.absolutePath}")
                Log.d(TAG, "文件大小: ${file.length()} 字节")
            } else {
                Log.e(TAG, "文件创建失败: ${file.absolutePath}")
            }

            // 回收Bitmap
            micrBitmap.recycle()

            return file.absolutePath
        } catch (e: Exception) {
            Log.e(TAG, "保存MICR图像时出错: ${e.message}", e)
            return null
        }
    }

    /**
     * 提取数字和符号
     */
    private fun extractDigitsAndSymbols(
        image: Mat,
        charContours: java.util.List<MatOfPoint>,
        minW: Int = 5,
        minH: Int = 19
    ): Pair<java.util.List<Mat>, java.util.List<OpenCVRect>> {
        val rois = ArrayList<Mat>()
        val locs = ArrayList<OpenCVRect>()
        val iterator = charContours.iterator()

        val imageWidth = image.cols()
        val imageHeight = image.rows()

        try {
            //Log.d(TAG, "开始提取字符，图像大小: 宽=${imageWidth}, 高=${imageHeight}, 轮廓数量=${charContours.size}")
            
            while (iterator.hasNext()) {
                val c = iterator.next()
                val rect = Imgproc.boundingRect(c)
                
                //Log.d(TAG, "原始轮廓矩形: x=${rect.x}, y=${rect.y}, w=${rect.width}, h=${rect.height}")

                if (rect.width >= minW && rect.height >= minH) {
                    val safeRect = clampRect(rect, imageWidth, imageHeight)
                    if (safeRect.width > 0 && safeRect.height > 0) {
                        val roi = image.submat(safeRect)
                        rois.add(roi.clone())
                        locs.add(safeRect)
                    }
                } else if (iterator.hasNext()) {
                    try {
                        val parts = listOf(c, iterator.next(), iterator.next())
                        var sXA = Int.MAX_VALUE
                        var sYA = Int.MAX_VALUE
                        var sXB = Int.MIN_VALUE
                        var sYB = Int.MIN_VALUE

                        for (p in parts) {
                            val r = Imgproc.boundingRect(p)
                            sXA = minOf(sXA, r.x)
                            sYA = minOf(sYA, r.y)
                            sXB = maxOf(sXB, r.x + r.width)
                            sYB = maxOf(sYB, r.y + r.height)
                        }

                        val combinedRect = OpenCVRect(sXA, sYA, sXB - sXA, sYB - sYA)
                        val safeRect = clampRect(combinedRect, imageWidth, imageHeight)

                        if (safeRect.width > 0 && safeRect.height > 0) {
                            val roi = image.submat(safeRect)
                            rois.add(roi.clone())
                            locs.add(safeRect)
                            //Log.d(TAG, "成功提取合并ROI")
                        } else {
                            //Log.e(TAG, "合并ROI边界无效")
                        }
                    } catch (e: Exception) {
                        //Log.e(TAG, "合并ROI失败: ${e.message}")
                    }
                }
            }

            Log.d(TAG, "字符提取完成，共提取: ${rois.size} 个字符")
            return Pair(rois as java.util.List<Mat>, locs as java.util.List<OpenCVRect>)
        } catch (e: Exception) {
            Log.e(TAG, "字符提取过程出错: ${e.message}")
            throw e
        }
    }

    fun clampRect(rect: OpenCVRect, imageWidth: Int, imageHeight: Int): OpenCVRect {
        val x = rect.x.coerceIn(0, imageWidth)
        val y = rect.y.coerceIn(0, imageHeight)
        val width = if (x + rect.width > imageWidth) imageWidth - x else rect.width
        val height = if (y + rect.height > imageHeight) imageHeight - y else rect.height
        return OpenCVRect(x, y, width, height)
    }
}