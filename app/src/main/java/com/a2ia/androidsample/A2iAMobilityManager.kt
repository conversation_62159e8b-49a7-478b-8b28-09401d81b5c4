package com.a2ia.androidsample

import android.content.Context
import android.util.Log
import com.a2ia.Engine
import com.a2ia.androidsample.utils.AsyncZipExtract
import com.a2ia.data.*
import com.a2ia.data.Unit as Unt
import com.a2ia.data.Currency
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.text.DateFormat
import java.util.*
import java.text.SimpleDateFormat

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import java.io.ByteArrayOutputStream


const val LICENSE = "T2EJ4GREVZSXR7YELZ438LUFMCZMP6M74E45Z3HD2JA85QVEMXQAM22KMZ3TA62JDMSZMFJ8YRWHSMDXWDRPNDZGNH4SNSBCLQB4852SC7UCT98K2AVWBE2C3M8D6Q9YY9H6RU3SXY938KS7CT2WBFGNJC"
lateinit var engine: Engine
var enginePath: String = ""
lateinit var countryName : String
lateinit var country: Country
lateinit var currency: Currency


abstract class A2iAMobilityManager {

    companion object {
        var initialized: Boolean = false
        private val reusableStream = ByteArrayOutputStream()  

        //this method is only use by DocumentDetectorManager. The goal is to use the licence in only one class is the whole app
        @Throws(Exception::class)
        fun getEngine(): Engine? {
            if (!initialized) {
                instantiateEngine(enginePath)
                initialized = true
            }
            return engine
        }

        @Throws(Exception::class)
        private fun instantiateEngine(enginePath: String) {
            engine = Engine(enginePath)
            try {
                engine.setLicense(LICENSE)
            }catch (e: java.lang.Exception) {
                e.printStackTrace()
            }
            //uncomment this line to activate traces
            //engine.activateTraces(0);
        }



        private fun createImageConfig(
            image: ByteArray?,
            convertedOriginalFormat: OutputFormat = OutputFormat.PNG,
            preprocessedFormat: OutputFormat = OutputFormat.PNG,
            locatedDocumentFormat: OutputFormat = OutputFormat.PNG
        ): com.a2ia.data.Input {
            val input = com.a2ia.data.Input()

            val memoryImage = MemoryImage()
            memoryImage.imageFormat = ImageFormat.JPEG
            memoryImage.buffer = image
            input.image = memoryImage

            // 配置详细输出
            input.verbose = A2iABoolean.Yes

            // 配置三个图像变量的输出格式
            input.verboseDetails.preprocessedImage = A2iABoolean.Yes
            input.verboseDetails.preprocessedImageFormat.outputFormat = preprocessedFormat

            input.verboseDetails.locatedDocumentImage = A2iABoolean.Yes
            input.verboseDetails.locatedDocumentImageFormat.outputFormat = locatedDocumentFormat

            return input
        }


        fun recognizeCheck(context: Context, imageToProcess: ByteArray?): String {
            var result = ""
        
            try {
                // Get A2iACheckInput instance for image processing
                val checkInput = com.a2ia.data.CheckInput.getInstance(context)
        
                // Convert byte array to InputStream
                val inputStream = imageToProcess?.inputStream()
                if (inputStream == null) {
                    return "Image data is empty"
                }
        
                // Process image
                val checkOutput = checkInput.processImage(inputStream)
        
                // Check if processing was successful
                if (checkOutput.getResult(com.a2ia.data.CheckDocumentResults.KEY_IS_SUCCESSFUL) != true) {
                    return "Processing failed: ${checkOutput.getResult(com.a2ia.data.CheckDocumentResults.KEY_ERROR_MESSAGE)}"
                }

                val imageConfig = createImageConfig(imageToProcess)

                val output = com.a2ia.data.Output()

                output.setImageData(imageToProcess, checkOutput, imageConfig)

                IMAGE_PREPROCESSED = output.preprocessedImage.buffer 

                // 保存文件（如果需要）
                try {
                    val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                    val directory = File(context.getExternalFilesDir(null), "processed_checks")
                    if (!directory.exists()) {
                        directory.mkdirs()
                    }

                    // 保存转换后的原始图像
                    output.convertedOriginalImage.buffer?.let { data ->
                        reusableStream.reset()
                        reusableStream.write(data)
                        val file = File(directory, "convertedOriginal_${timestamp}.${output.convertedOriginalImage.imageFormat}")
                        FileOutputStream(file).use { out ->
                            out.write(reusableStream.toByteArray())
                        }
                        result += String.format(Locale.US, "- Converted original image saved to: %s\n\n", file.absolutePath)
                    }

                    // 保存预处理黑白图像
                    output.preprocessedImage.buffer?.let { data ->
                        reusableStream.reset()
                        reusableStream.write(data)
                        val file = File(directory, "preprocessed_${timestamp}.${output.preprocessedImage.imageFormat}")
                        FileOutputStream(file).use { out ->
                            out.write(reusableStream.toByteArray())
                        }
                        result += String.format(Locale.US, "- Preprocessed image saved to: %s\n\n", file.absolutePath)
                    }

                    // 保存定位文档图像
                    output.locatedDocumentImage.buffer?.let { data ->
                        reusableStream.reset()
                        reusableStream.write(data)
                        val file = File(directory, "locatedDocument_${timestamp}.${output.locatedDocumentImage.imageFormat}")
                        FileOutputStream(file).use { out ->
                            out.write(reusableStream.toByteArray())
                        }
                        result += String.format(Locale.US, "- Located document image saved to: %s\n\n", file.absolutePath)
                    }

                } catch (e: Exception) {
                    Log.e("A2iAMobilityManager", "Failed to save custom images", e)
                    // 注意这里的异常不影响原有流程
                }

                // Status OK message
                result += "Status OK\n\n"
        
                // Amount
                result += String.format(Locale.US, "- Amount: %s (score %d)", 
                    checkOutput.result.amount.toDouble() / 100.00 ?: "- Amount no result", 
                    checkOutput.result.score)
                result += "\n\n"
            
                // CAR
                if (checkOutput.larListItemsCount > 0) {
                    result += String.format(Locale.US, "- CAR %.2f (score %d)", 
                        checkOutput.getCARListItem(0).amount.toDouble() / 100.00, 
                        (checkOutput.getCARListItem(0).prob * 1000).toInt())
                    result += "\n\n"
                } else {
                    result += "- CAR no result\n\n"
                }
    
                // Codeline
                result += String.format(Locale.US, "- Codeline: %s (score %d)", 
                    checkOutput.codeline.result.reco ?: "", 
                    checkOutput.codeline.result.score)
                result += "\n\n"
            
                // Payee Name
                result += String.format(Locale.US, "- Payee Name: %s (score %d)", 
                    checkOutput.payeeName.result.reco ?: "", 
                    checkOutput.payeeName.result.score)
                result += "\n\n"
            
                // Date
                result += String.format(Locale.US, "- Date: %s (score %d)", 
                    checkOutput.date.result.reco ?: "", 
                    checkOutput.date.result.score)
                result += "\n\n"

                // Check Number
                result += String.format(Locale.US, "- Check Number: %s (score %d)",
                    checkOutput.checkNumber.result.reco ?: "",
                    checkOutput.checkNumber.result.score)
                result += "\n\n"

                // Signature detection
                result += String.format(Locale.US, "- Invalidity Signature: %s (score %d)", 
                    if (checkOutput.invalidity.noSignature) "Signature not found" else "Signature found",
                    checkOutput.invalidity.noSignatureScore)
                result += "\n\n"
            
                // Security Codes
                result += String.format(Locale.US, "- Security Codes: %s (score %d)", 
                    checkOutput.securityCode1.result.reco ?: "", 
                    checkOutput.securityCode1.result.score)
                result += "\n\n"

                // Address
                val cpt = 0
                result += String.format(Locale.US, "- Payer Address: %s (score %d)", 
                    checkOutput.address.getLineItem(cpt).reco ?: "", 
                    checkOutput.address.getLineItem(cpt).score)
                result += "\n\n"

                // Genuine
                result += "- Genuine: ${if (checkOutput.getResult(com.a2ia.data.CheckDocumentResults.KEY_IS_GENUINE) == true) "Real Cheque" else "Printed Cheque"}\n\n"

                // Has Valid Cheque Contour
                if (checkOutput.getResult(com.a2ia.data.CheckDocumentResults.KEY_HAS_VALID_CHEQUE_CONTOUR) == false) {
                    result += String.format(Locale.US, "%s", "- Cheque not found, please retake again")
                } else if (checkOutput.getResult(com.a2ia.data.CheckDocumentResults.KEY_VALID_MICR_POSITION) == false) {
                    result += String.format(Locale.US, "%s", "- MICR not found, please retake again")
                }
                result += "\n\n"
        
            } catch (e: Exception) {
                e.printStackTrace()
                result = "Processing exception: ${e.message}"
            }
        
            return result
        }

        private fun dummyResult(): String {
            return "Unable to use this function after the removal of the engine"
        }

        /**
         * This method is used to have a [Engine] running with the correct data for the country we need.
         * This method only avoid to repeat a lot of identitical code several time.
         * @param context the context, we need it to access to assets, where the zip files are, and to the files dire where we will unzip the parms file
         * @param countryName the name of the country we want, by convention the zip files will be countryName.zip
         * @return a setup [Engine]
         * @throws Exception if the engine can be construct with the parms file, or of something goes wrong with the unzipping
         */
        @Throws(Exception::class)
        fun setupEngine(context: Context, countryName: String) {
            if (!initialized) {
                
                enginePath = context.filesDir.absolutePath + "/" + countryName
                getEngine()
            }
        }

        /**
         * This method clean the engine if the engine is not null.
         * It is only some code factorization to avoid repeating the code `if (engine != null)`.
         */
        fun clearEngine() {
            if (initialized) {
                engine.clean()
            }
        }

        @Throws(Exception::class)
        private fun copyAsset(assetFile: InputStream, destination: String, clean: Boolean) {
            val file = File(destination)
            if (file.exists() || clean) {
                file.delete()
                val fos = FileOutputStream(file)
                val buffer = ByteArray(1024)
                var read: Int
                do {
                    read = assetFile.read(buffer)
                    fos.write(buffer, 0, read)
                }
                while (read != -1)

                fos.flush()
                fos.close()
                assetFile.close()
            }
        }



        /**
         * 便捷方法：使用默认格式创建图像配置
         */
        private fun createImageConfig(image: ByteArray?): com.a2ia.data.Input {
            return createImageConfig(image, OutputFormat.JPEG, OutputFormat.JPEG, OutputFormat.JPEG)
        }

        fun recognizeIdentity(context: Context, imageToProcess: ByteArray?): String {
            return dummyResult()
        }

        fun recognizeRIB(context: Context, imageToProcess: ByteArray?): String {
            return dummyResult()
        }

        fun recognizeProofOfResidency(context: Context, imageToProcess: ByteArray?): String {
            return dummyResult()
        }

        fun recognizeReceipt(context: Context, imageToProcess: ByteArray?): String {
            return dummyResult()
        }

        fun recognizeInvoice(context: Context, imageToProcess: ByteArray?): String {
            return dummyResult()
        }

        fun recognizeLocation(context: Context, imageToProcess: ByteArray?): String {
            return dummyResult()
        }

        fun recognizeSingleField(context: Context, imageToProcess: ByteArray?): String {
            return dummyResult()
        }

        fun recognizeCustomDocument(context: Context, imageToProcess: ByteArray?): String {
            return dummyResult()
        }

        fun recognizeBillPay(context: Context, imageToProcess: ByteArray?): String {
            return dummyResult()
        }

        fun recognizeGeneric(context: Context, imageToProcess: ByteArray?): String {
            return dummyResult()
        }

        fun recognizeTaxAssessment(context: Context, imageToProcess: ByteArray?): String {
            return dummyResult()
        }

        fun defineCountry(context: Context) {
            val sharedPreferences = context.getSharedPreferences("CountryName",
                    Context.MODE_PRIVATE)
            countryName = sharedPreferences.getString("countryCode", "FR")!!

            when (countryName) {
                "FR" -> {
                    country = Country.FR;
                    currency = Currency.EUR;
                }
                "US" -> {
                    country = Country.US;
                    currency = Currency.USD;
                }
                "AR" -> {
                    country = Country.AR;
                    currency = Currency.USD;
                }
                "AU" -> {
                    country = Country.AU;
                    currency = Currency.AUD;
                }
                "BR" -> {
                    country = Country.BR;
                    currency = Currency.BRL;
                }
                "CA" -> {
                    country = Country.CA;
                    currency = Currency.CAD;
                }
                "CL" -> {
                    country = Country.CL;
                    currency = Currency.CLP;
                }
                "CM" -> {
                    country = Country.CM;
                    currency = Currency.NotDefined;
                }
                "CO" -> {
                    country = Country.CO;
                    currency = Currency.COP;
                }
                "CY" -> {
                    country = Country.CY;
                    currency = Currency.EUR;
                }
                "EC" -> {
                    country = Country.EC;
                    currency = Currency.USD;
                }
                "EU" -> {
                    country = Country.Europe;
                    currency = Currency.EUR;
                }
                "GN" -> {
                    country = Country.Generic;
                    currency = Currency.USD;
                }
                "HK" -> {
                    country = Country.HK;
                    currency = Currency.HKD;
                }
                "HN" -> {
                    country = Country.HN;
                    currency = Currency.HNL;
                }
                "IN" -> {
                    country = Country.IN;
                    currency = Currency.INR;
                }
                "IT" -> {
                    country = Country.IT;
                    currency = Currency.EUR;
                }
                "MA" -> {
                    country = Country.MA;
                    currency = Currency.MAD;
                }
                "MX" -> {
                    country = Country.MX;
                    currency = Currency.MXP;
                }
                "PT" -> {
                    country = Country.PT;
                    currency = Currency.EUR;
                }
                "SG" -> {
                    country = Country.SG;
                    currency = Currency.SGD;
                }
                "UK" -> {
                    country = Country.UK;
                    currency = Currency.GBP;
                }
            }
        }

   


    }
}