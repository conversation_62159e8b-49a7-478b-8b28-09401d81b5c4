package com.a2ia.androidsample

import android.content.Context
import android.util.Log
import com.a2ia.data.ImageOutputType
import java.io.File
import java.io.FileOutputStream

/**
 * 灵活图像输出示例
 * 展示如何让 IMAGE_PREPROCESSED 可以选择输出不同的图像
 */
class FlexibleImageOutputExample {
    
    companion object {
        private const val TAG = "FlexibleImageOutput"
        
        /**
         * 示例1：选择转换后的原始图像作为 IMAGE_PREPROCESSED
         */
        fun useConvertedOriginalAsPreprocessed(context: Context, imageData: ByteArray?) {
            try {
                Log.d(TAG, "使用转换后的原始图像作为 IMAGE_PREPROCESSED")
                
                // 处理图像
                val output = A2iAMobilityManager.processImageWithBuffer(context, imageData)
                
                // 选择转换后的原始图像作为 IMAGE_PREPROCESSED
                val imagePreprocessed = output.getImagePreprocessed(ImageOutputType.CONVERTED_ORIGINAL)
                
                if (imagePreprocessed != null) {
                    saveImageToFile(context, imagePreprocessed, "preprocessed_from_original.jpg")
                    Log.d(TAG, "IMAGE_PREPROCESSED 现在是转换后的原始图像")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "处理失败: ${e.message}")
            }
        }
        
        /**
         * 示例2：选择预处理图像（黑白版）作为 IMAGE_PREPROCESSED
         */
        fun usePreprocessedAsPreprocessed(context: Context, imageData: ByteArray?) {
            try {
                Log.d(TAG, "使用预处理图像（黑白版）作为 IMAGE_PREPROCESSED")
                
                // 处理图像
                val output = A2iAMobilityManager.processImageWithBuffer(context, imageData)
                
                // 选择预处理图像作为 IMAGE_PREPROCESSED（默认选择）
                val imagePreprocessed = output.getImagePreprocessed(ImageOutputType.PREPROCESSED)
                
                if (imagePreprocessed != null) {
                    saveImageToFile(context, imagePreprocessed, "preprocessed_from_grayscale.jpg")
                    Log.d(TAG, "IMAGE_PREPROCESSED 现在是预处理黑白图像")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "处理失败: ${e.message}")
            }
        }
        
        /**
         * 示例3：选择定位文档图像作为 IMAGE_PREPROCESSED
         */
        fun useLocatedDocumentAsPreprocessed(context: Context, imageData: ByteArray?) {
            try {
                Log.d(TAG, "使用定位文档图像作为 IMAGE_PREPROCESSED")
                
                // 处理图像
                val output = A2iAMobilityManager.processImageWithBuffer(context, imageData)
                
                // 选择定位文档图像作为 IMAGE_PREPROCESSED
                val imagePreprocessed = output.getImagePreprocessed(ImageOutputType.LOCATED_DOCUMENT)
                
                if (imagePreprocessed != null) {
                    saveImageToFile(context, imagePreprocessed, "preprocessed_from_located.jpg")
                    Log.d(TAG, "IMAGE_PREPROCESSED 现在是定位文档图像")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "处理失败: ${e.message}")
            }
        }
        
        /**
         * 示例4：动态选择 IMAGE_PREPROCESSED 的输出
         */
        fun dynamicImagePreprocessedSelection(context: Context, imageData: ByteArray?, userChoice: String) {
            try {
                Log.d(TAG, "根据用户选择动态设置 IMAGE_PREPROCESSED: $userChoice")
                
                // 处理图像
                val output = A2iAMobilityManager.processImageWithBuffer(context, imageData)
                
                // 根据用户选择决定 IMAGE_PREPROCESSED 的来源
                val imageType = when (userChoice.lowercase()) {
                    "original" -> ImageOutputType.CONVERTED_ORIGINAL
                    "grayscale", "bw" -> ImageOutputType.PREPROCESSED
                    "located", "document" -> ImageOutputType.LOCATED_DOCUMENT
                    else -> ImageOutputType.PREPROCESSED // 默认选择
                }
                
                val imagePreprocessed = output.getImagePreprocessed(imageType)
                
                if (imagePreprocessed != null) {
                    val fileName = "preprocessed_${userChoice.lowercase()}.jpg"
                    saveImageToFile(context, imagePreprocessed, fileName)
                    Log.d(TAG, "IMAGE_PREPROCESSED 已设置为: $userChoice")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "处理失败: ${e.message}")
            }
        }
        
        /**
         * 示例5：比较三种不同的 IMAGE_PREPROCESSED 输出
         */
        fun compareAllImagePreprocessedOptions(context: Context, imageData: ByteArray?) {
            try {
                Log.d(TAG, "比较所有 IMAGE_PREPROCESSED 选项")
                
                // 处理图像
                val output = A2iAMobilityManager.processImageWithBuffer(context, imageData)
                
                // 获取三种不同的输出
                val fromOriginal = output.getImagePreprocessed(ImageOutputType.CONVERTED_ORIGINAL)
                val fromPreprocessed = output.getImagePreprocessed(ImageOutputType.PREPROCESSED)
                val fromLocated = output.getImagePreprocessed(ImageOutputType.LOCATED_DOCUMENT)
                
                // 保存并比较
                fromOriginal?.let { 
                    saveImageToFile(context, it, "compare_original.jpg")
                    Log.d(TAG, "原始图像版本大小: ${it.size} bytes")
                }
                
                fromPreprocessed?.let { 
                    saveImageToFile(context, it, "compare_preprocessed.jpg")
                    Log.d(TAG, "预处理版本大小: ${it.size} bytes")
                }
                
                fromLocated?.let { 
                    saveImageToFile(context, it, "compare_located.jpg")
                    Log.d(TAG, "定位文档版本大小: ${it.size} bytes")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "处理失败: ${e.message}")
            }
        }
        
        /**
         * 保存图像到文件的辅助方法
         */
        private fun saveImageToFile(context: Context, imageData: ByteArray, fileName: String) {
            try {
                val file = File(context.filesDir, fileName)
                FileOutputStream(file).use { out ->
                    out.write(imageData)
                }
                Log.d(TAG, "图像已保存到: ${file.absolutePath}")
            } catch (e: Exception) {
                Log.e(TAG, "保存图像失败: ${e.message}")
            }
        }
    }
}
