package com.a2ia.androidsample

import android.content.Context
import android.util.Log
import com.a2ia.data.OutputFormat
import java.io.File
import java.io.FileOutputStream

/**
 * 使用新的 buffer 方式的示例类
 * 展示如何从 processedData = ImageConverter.convertImage() 
 * 改为 output.PreprocessedImage.buffer 的使用方式
 */
class BufferUsageExample {
    
    companion object {
        private const val TAG = "BufferUsageExample"
        
        /**
         * 旧的使用方式示例（您之前的代码）
         */
        fun oldWayExample(context: Context, imageData: ByteArray?) {
            try {
                // 旧的方式：手动调用 ImageConverter
                val format = OutputFormat.JPEG
                val processedData = com.a2ia.data.ImageConverter.convertImage(imageData!!, format)
                
                // 保存文件
                val file = File(context.filesDir, "old_way_image.jpg")
                FileOutputStream(file).use { out ->
                    out.write(processedData)
                }
                
                Log.d(TAG, "旧方式：图像已保存到 ${file.absolutePath}")
            } catch (e: Exception) {
                Log.e(TAG, "旧方式处理失败: ${e.message}")
            }
        }
        
        /**
         * 新的使用方式示例（推荐使用）
         */
        fun newWayExample(context: Context, imageData: ByteArray?) {
            try {
                // 新的方式：使用 A2iAMobilityManager 的新方法
                val output = A2iAMobilityManager.processImageWithBuffer(context, imageData)
                
                // 直接从 output.PreprocessedImage.buffer 获取处理后的图像
                val processedImageData = output.PreprocessedImage.buffer
                
                if (processedImageData != null) {
                    // 保存文件
                    val file = File(context.filesDir, "new_way_image.jpg")
                    FileOutputStream(file).use { out ->
                        out.write(processedImageData)
                    }
                    
                    Log.d(TAG, "新方式：图像已保存到 ${file.absolutePath}")
                    Log.d(TAG, "图像格式：${output.PreprocessedImage.imageFormat}")
                    Log.d(TAG, "图像大小：${processedImageData.size} bytes")
                } else {
                    Log.w(TAG, "新方式：没有处理后的图像数据")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "新方式处理失败: ${e.message}")
            }
        }
        
        /**
         * 完整的使用流程示例
         */
        fun completeUsageExample(context: Context, imageData: ByteArray?) {
            try {
                Log.d(TAG, "开始完整的图像处理流程...")
                
                // 1. 使用新的处理方法
                val output = A2iAMobilityManager.processImageWithBuffer(context, imageData)
                
                // 2. 检查处理状态
                if (output.status == com.a2ia.data.Status.OK) {
                    Log.d(TAG, "图像处理成功")
                    
                    // 3. 获取处理后的图像数据 - 这就是您想要的简洁方式！
                    val processedImage = output.PreprocessedImage.buffer
                    
                    if (processedImage != null) {
                        Log.d(TAG, "成功获取处理后的图像，大小: ${processedImage.size} bytes")
                        
                        // 4. 您可以直接使用这个 buffer 数据
                        // 比如保存到文件、显示在界面上、或者进行进一步处理
                        saveImageToFile(context, processedImage, "complete_example.jpg")
                        
                    } else {
                        Log.w(TAG, "处理后的图像数据为空")
                    }
                } else {
                    Log.e(TAG, "图像处理失败: ${output.statusContext}")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "完整流程处理失败: ${e.message}")
            }
        }
        
        /**
         * 保存图像到文件的辅助方法
         */
        private fun saveImageToFile(context: Context, imageData: ByteArray, fileName: String) {
            try {
                val file = File(context.filesDir, fileName)
                FileOutputStream(file).use { out ->
                    out.write(imageData)
                }
                Log.d(TAG, "图像已保存到: ${file.absolutePath}")
            } catch (e: Exception) {
                Log.e(TAG, "保存图像失败: ${e.message}")
            }
        }
    }
}
