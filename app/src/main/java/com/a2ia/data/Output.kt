package com.a2ia.data

class Output {
    val status = Status.OK
    val statusContext = ""
    val PreprocessedImage = ProcessedImageOutput()
    val documentResults: Any? = null
}

/**
 * 处理后图像输出类
 * 提供简洁的 buffer 访问方式
 */
class ProcessedImageOutput {
    var buffer: ByteArray? = null
    var imageFormat: ImageFormat = ImageFormat.JPEG

    /**
     * 设置处理后的图像数据
     */
    fun setImageData(imageData: ByteArray?, format: OutputFormat = OutputFormat.JPEG) {
        if (imageData != null) {
            buffer = ImageConverter.convertImage(imageData, format)
            imageFormat = when (format) {
                OutputFormat.JPEG -> ImageFormat.JPEG
                OutputFormat.PNG -> ImageFormat.PNG
            }
        }
    }

    /**
     * 设置灰度图像数据
     */
    fun setGrayscaleImageData(imageData: ByteArray?, format: OutputFormat = OutputFormat.JPEG) {
        if (imageData != null) {
            buffer = ImageConverter.convertImage(imageData, format, grayscale = true)
            imageFormat = when (format) {
                OutputFormat.JPEG -> ImageFormat.JPEG
                OutputFormat.PNG -> ImageFormat.PNG
            }
        }
    }
}

enum class Status {
    OK, ERROR
}