 com/a2ia/DocumentDetectionResultcom/a2ia/DocumentDetectorcom/a2ia/Engine*com/a2ia/androidsample/A2iAMobilityManager4com/a2ia/androidsample/A2iAMobilityManager$Companion,com/a2ia/androidsample/A2iAMobilityManagerKt$com/a2ia/androidsample/ImageHelperKt%com/a2ia/androidsample/ImageProcessor&com/a2ia/androidsample/ProcessActivity(com/a2ia/androidsample/ProcessActivityKt$com/a2ia/androidsample/StartActivity.com/a2ia/androidsample/StartActivity$Companion0com/a2ia/androidsample/StartActivity$DemoAdapter;com/a2ia/androidsample/StartActivity$DemoAdapter$ViewHolder5com/a2ia/androidsample/StartActivity$CountriesAdapter@com/a2ia/androidsample/StartActivity$CountriesAdapter$ViewHolder&com/a2ia/androidsample/StartActivityKt<com/a2ia/androidsample/documentdetector/A2IADetectedDocumentFcom/a2ia/androidsample/documentdetector/A2IADetectedDocument$CompanionAcom/a2ia/androidsample/documentdetector/DocumentDetectionActivityKcom/a2ia/androidsample/documentdetector/DocumentDetectionActivity$CompanionAcom/a2ia/androidsample/documentdetector/DocumentDetectionCallback=com/a2ia/androidsample/documentdetector/DocumentDetectionTaskGcom/a2ia/androidsample/documentdetector/DocumentDetectionTask$Companion>com/a2ia/androidsample/documentdetector/DocumentDetectionTask2Hcom/a2ia/androidsample/documentdetector/DocumentDetectionTask2$Companion>com/a2ia/androidsample/documentdetector/DocumentDetectorCommonOcom/a2ia/androidsample/documentdetector/DocumentDetectorCommon$DrawingBoxesMode>com/a2ia/androidsample/documentdetector/DocumentDetectorHelperHcom/a2ia/androidsample/documentdetector/DocumentDetectorHelper$Companion?com/a2ia/androidsample/documentdetector/DocumentDetectorManager@com/a2ia/androidsample/documentdetector/DocumentDetectorManager2Ucom/a2ia/androidsample/documentdetector/DocumentDetectorManager2$CapturedImageProcess-com/a2ia/androidsample/documentdetector/Tools8com/a2ia/androidsample/documentdetector/Tools$SimpleSize/com/a2ia/androidsample/documentdetector/ToolsKt,com/a2ia/androidsample/utils/AsyncZipExtractcom/a2ia/data/Booleancom/a2ia/data/Countrycom/a2ia/data/Currency com/a2ia/data/CharacterFieldTypecom/a2ia/data/WriteTypecom/a2ia/data/Origincom/a2ia/data/PaperSizecom/a2ia/data/TransportModel#com/a2ia/data/OrientationCorrectioncom/a2ia/data/OutputFormatcom/a2ia/data/Unitcom/a2ia/data/Input com/a2ia/data/ImagePreprocessingcom/a2ia/data/DocumentLocationcom/a2ia/data/DocumentSizecom/a2ia/data/VerboseDetailscom/a2ia/data/ImageFormat#com/a2ia/data/ImageFormat$Companioncom/a2ia/data/MemoryImagecom/a2ia/data/SpecificDocument+com/a2ia/data/IdentityDocumentSpecificInput1com/a2ia/data/DrivingLicenseIdentityDocumentInputcom/a2ia/data/FieldEnablecom/a2ia/data/RIBSpecificInput+com/a2ia/data/ProofOfResidencySpecificInput"com/a2ia/data/ReceiptSpecificInputcom/a2ia/data/Receipt"com/a2ia/data/InvoiceSpecificInputcom/a2ia/data/Invoice(com/a2ia/data/TaxAssessmentSpecificInput!com/a2ia/data/SingleFieldDocument"com/a2ia/data/CharactersFieldInputcom/a2ia/data/FieldInfocom/a2ia/data/CustomDocumentcom/a2ia/data/CustomInputcom/a2ia/data/Boxcom/a2ia/data/InputKtcom/a2ia/data/Outputcom/a2ia/data/Status.kotlin_module)com/a2ia/androidsample/BufferUsageExample3com/a2ia/androidsample/BufferUsageExample$Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              