/ Header Record For PersistentHashMapValueStorage6 5app/src/main/java/com/a2ia/DocumentDetectionResult.kt/ .app/src/main/java/com/a2ia/DocumentDetector.kt% $app/src/main/java/com/a2ia/Engine.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt; :app/src/main/java/com/a2ia/androidsample/ImageProcessor.kt< ;app/src/main/java/com/a2ia/androidsample/ProcessActivity.kt: 9app/src/main/java/com/a2ia/androidsample/StartActivity.kt: 9app/src/main/java/com/a2ia/androidsample/StartActivity.kt: 9app/src/main/java/com/a2ia/androidsample/StartActivity.kt: 9app/src/main/java/com/a2ia/androidsample/StartActivity.kt: 9app/src/main/java/com/a2ia/androidsample/StartActivity.kt: 9app/src/main/java/com/a2ia/androidsample/StartActivity.ktR Qapp/src/main/java/com/a2ia/androidsample/documentdetector/A2IADetectedDocument.ktR Qapp/src/main/java/com/a2ia/androidsample/documentdetector/A2IADetectedDocument.ktW Vapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionActivity.ktW Vapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionActivity.ktW Vapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionCallback.ktS Rapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionTask.ktS Rapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionTask.ktT Sapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionTask2.ktT Sapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionTask2.ktT Sapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorCommon.ktT Sapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorCommon.ktT Sapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorHelper.ktT Sapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorHelper.ktU Tapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorManager.ktV Uapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorManager2.ktV Uapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorManager2.ktC Bapp/src/main/java/com/a2ia/androidsample/documentdetector/Tools.ktC Bapp/src/main/java/com/a2ia/androidsample/documentdetector/Tools.ktB Aapp/src/main/java/com/a2ia/androidsample/utils/AsyncZipExtract.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt* )app/src/main/java/com/a2ia/data/Output.kt* )app/src/main/java/com/a2ia/data/Output.ktB Aapp/src/main/java/com/a2ia/androidsample/utils/UnzipCallback.javaU Tapp/build/generated/source/buildConfig/debug/com/a2ia/androidsample/BuildConfig.java@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Enums.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt) (app/src/main/java/com/a2ia/data/Input.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt% $app/src/main/java/com/a2ia/Engine.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt? >app/src/main/java/com/a2ia/androidsample/BufferUsageExample.kt? >app/src/main/java/com/a2ia/androidsample/BufferUsageExample.kt% $app/src/main/java/com/a2ia/Engine.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt% $app/src/main/java/com/a2ia/Engine.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt% $app/src/main/java/com/a2ia/Engine.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt@ ?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt