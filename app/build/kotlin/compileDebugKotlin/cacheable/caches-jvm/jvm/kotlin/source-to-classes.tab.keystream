5app/src/main/java/com/a2ia/DocumentDetectionResult.kt.app/src/main/java/com/a2ia/DocumentDetector.kt$app/src/main/java/com/a2ia/Engine.kt?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt7app/src/main/java/com/a2ia/androidsample/ImageHelper.kt:app/src/main/java/com/a2ia/androidsample/ImageProcessor.kt;app/src/main/java/com/a2ia/androidsample/ProcessActivity.kt9app/src/main/java/com/a2ia/androidsample/StartActivity.ktQapp/src/main/java/com/a2ia/androidsample/documentdetector/A2IADetectedDocument.ktVapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionActivity.ktVapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionCallback.ktRapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionTask.ktSapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionTask2.ktSapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorCommon.ktSapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorHelper.ktTapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorManager.ktUapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorManager2.ktBapp/src/main/java/com/a2ia/androidsample/documentdetector/Tools.ktAapp/src/main/java/com/a2ia/androidsample/utils/AsyncZipExtract.kt(app/src/main/java/com/a2ia/data/Enums.kt(app/src/main/java/com/a2ia/data/Input.kt)app/src/main/java/com/a2ia/data/Output.ktAapp/src/main/java/com/a2ia/androidsample/utils/UnzipCallback.javaTapp/build/generated/source/buildConfig/debug/com/a2ia/androidsample/BuildConfig.java>app/src/main/java/com/a2ia/androidsample/BufferUsageExample.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            