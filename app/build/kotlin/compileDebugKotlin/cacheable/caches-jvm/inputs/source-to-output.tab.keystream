Sapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorCommon.kt:app/src/main/java/com/a2ia/androidsample/ImageProcessor.ktSapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionTask2.kt5app/src/main/java/com/a2ia/DocumentDetectionResult.ktVapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionActivity.kt.app/src/main/java/com/a2ia/DocumentDetector.kt9app/src/main/java/com/a2ia/androidsample/StartActivity.kt(app/src/main/java/com/a2ia/data/Input.ktQapp/src/main/java/com/a2ia/androidsample/documentdetector/A2IADetectedDocument.ktUapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorManager2.ktSapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorHelper.kt7app/src/main/java/com/a2ia/androidsample/ImageHelper.kt(app/src/main/java/com/a2ia/data/Enums.ktRapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionTask.ktTapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectorManager.ktAapp/src/main/java/com/a2ia/androidsample/utils/AsyncZipExtract.kt;app/src/main/java/com/a2ia/androidsample/ProcessActivity.kt)app/src/main/java/com/a2ia/data/Output.ktVapp/src/main/java/com/a2ia/androidsample/documentdetector/DocumentDetectionCallback.ktBapp/src/main/java/com/a2ia/androidsample/documentdetector/Tools.kt$app/src/main/java/com/a2ia/Engine.kt?app/src/main/java/com/a2ia/androidsample/A2iAMobilityManager.kt>app/src/main/java/com/a2ia/androidsample/BufferUsageExample.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   